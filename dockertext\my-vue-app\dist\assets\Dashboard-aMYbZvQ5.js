import{_ as V,c as g,o as $,a as r,b as d,d as q,e as t,f as e,r as v,w as a,g as p,u as n,h as z,i as R,t as l,j as F,k as I,l as L,F as k,m as w,n as b,E as C,p as M,q as j,s as O}from"./index-8CxuF_5j.js";import{u as U}from"./articles-VPDDBgOB.js";const G={class:"dashboard"},H={key:0,class:"loading-container"},J={key:1,class:"dashboard-header"},K={class:"header-actions"},P={key:2,class:"stats-grid"},Q={class:"stat-card"},W={class:"stat-icon total"},X={class:"stat-content"},Y={class:"stat-number"},Z={class:"stat-card"},tt={class:"stat-icon draft"},st={class:"stat-content"},et={class:"stat-number"},ot={class:"stat-card"},at={class:"stat-icon published"},it={class:"stat-content"},nt={class:"stat-number"},lt={class:"stat-card"},ct={class:"stat-icon views"},dt={class:"stat-content"},rt={class:"stat-number"},_t={class:"dashboard-content"},ut={class:"section"},vt={class:"quick-actions"},ht=["onClick"],pt={class:"action-content"},mt={class:"action-title"},ft={class:"action-description"},gt={class:"action-arrow"},bt={class:"section"},yt={class:"section-header"},kt={class:"articles-list"},wt=["onClick"],Ct={class:"article-content"},At={class:"article-title"},Dt={class:"article-meta"},St={class:"article-date"},Nt={class:"article-views"},xt={class:"article-actions"},Et={__name:"Dashboard",setup(Bt){const _=z(),u=U(),h=g(()=>u.stats),A=g(()=>u.recentArticles),m=g(()=>u.loading),D=[{title:"创建新文章",description:"开始写作新的内容",icon:"Edit",color:"primary",action:()=>_.push("/article/edit")},{title:"导入文件",description:"批量导入本地文件",icon:"Upload",color:"success",action:()=>_.push("/import")},{title:"文章管理",description:"查看和管理所有文章",icon:"Document",color:"info",action:()=>_.push("/articles")}],S=i=>i==="published"?"success":"warning",N=i=>i==="published"?"已发布":"草稿",x=i=>{if(!i)return"未知时间";try{const s=new Date(i);return isNaN(s.getTime())?"无效日期":s.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch{return"日期错误"}},y=async()=>{try{const i=new Date().toISOString().split("T")[0];await u.loadArticles({date:i}),await u.updateStats(),C.success("数据刷新成功")}catch{C.error("数据刷新失败")}};return $(async()=>{await y()}),(i,s)=>{const E=v("el-skeleton"),B=v("Refresh"),c=v("el-icon"),f=v("el-button"),T=v("el-tag");return d(),r("div",G,[m.value?(d(),r("div",H,[e(E,{rows:8,animated:""})])):(d(),r("div",J,[s[4]||(s[4]=t("div",{class:"header-content"},[t("h1",{class:"page-title"},"仪表板"),t("p",{class:"page-subtitle"},"欢迎回来！管理您的智能笔记系统")],-1)),t("div",K,[e(f,{onClick:y,loading:m.value},{default:a(()=>[e(c,null,{default:a(()=>[e(B)]),_:1}),s[2]||(s[2]=p(" 刷新数据 "))]),_:1,__:[2]},8,["loading"]),e(f,{type:"primary",icon:i.Edit,onClick:s[0]||(s[0]=o=>n(_).push("/article/edit"))},{default:a(()=>s[3]||(s[3]=[p(" 创建文章 ")])),_:1,__:[3]},8,["icon"])])])),m.value?q("",!0):(d(),r("div",P,[t("div",Q,[t("div",W,[e(c,null,{default:a(()=>[e(n(R))]),_:1})]),t("div",X,[t("div",Y,l(h.value.totalArticles),1),s[5]||(s[5]=t("div",{class:"stat-label"},"总文章数",-1))])]),t("div",Z,[t("div",tt,[e(c,null,{default:a(()=>[e(n(F))]),_:1})]),t("div",st,[t("div",et,l(h.value.draftArticles),1),s[6]||(s[6]=t("div",{class:"stat-label"},"草稿",-1))])]),t("div",ot,[t("div",at,[e(c,null,{default:a(()=>[e(n(I))]),_:1})]),t("div",it,[t("div",nt,l(h.value.publishedArticles),1),s[7]||(s[7]=t("div",{class:"stat-label"},"已发布",-1))])]),t("div",lt,[t("div",ct,[e(c,null,{default:a(()=>[e(n(L))]),_:1})]),t("div",dt,[t("div",rt,l(h.value.todayArticles||0),1),s[8]||(s[8]=t("div",{class:"stat-label"},"今日文章",-1))])])])),t("div",_t,[t("div",ut,[s[9]||(s[9]=t("h2",{class:"section-title"},"快捷操作",-1)),t("div",vt,[(d(),r(k,null,w(D,o=>t("div",{key:o.title,class:"action-card",onClick:o.action},[t("div",{class:M(["action-icon",o.color])},[e(c,null,{default:a(()=>[(d(),j(O(o.icon)))]),_:2},1024)],2),t("div",pt,[t("h3",mt,l(o.title),1),t("p",ft,l(o.description),1)]),t("div",gt,[e(c,null,{default:a(()=>[e(n(b))]),_:1})])],8,ht)),64))])]),t("div",bt,[t("div",yt,[s[11]||(s[11]=t("h2",{class:"section-title"},"最近文章",-1)),e(f,{text:"",type:"primary",onClick:s[1]||(s[1]=o=>n(_).push("/articles"))},{default:a(()=>[s[10]||(s[10]=p(" 查看全部 ")),e(c,{class:"ml-1"},{default:a(()=>[e(n(b))]),_:1})]),_:1,__:[10]})]),t("div",kt,[(d(!0),r(k,null,w(A.value,o=>(d(),r("div",{key:o.id,class:"article-item",onClick:Tt=>n(_).push(`/article/edit/${o.id}`)},[t("div",Ct,[t("h3",At,l(o.title),1),t("div",Dt,[e(T,{type:S(o.status),size:"small"},{default:a(()=>[p(l(N(o.status)),1)]),_:2},1032,["type"]),t("span",St,l(x(o.created_at||o.createdAt)),1),t("span",Nt,l(o.views)+" 次浏览",1)])]),t("div",xt,[e(c,null,{default:a(()=>[e(n(b))]),_:1})])],8,wt))),128))])])])])}}},qt=V(Et,[["__scopeId","data-v-106a557b"]]);export{qt as default};
