# 集成新闻系统数据库API开发文档

## 📋 目录
- [数据库表结构](#数据库表结构)
- [数据接口规范](#数据接口规范)
- [API接口文档](#api接口文档)
- [集成指南](#集成指南)

---

## 🗄️ 数据库表结构

### 1. articles 表 - 文章主表

```sql
CREATE TABLE `articles` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '文章唯一ID',
  `title` varchar(500) NOT NULL COMMENT '文章标题',
  `content` longtext NOT NULL COMMENT '文章正文内容',
  `summary` text COMMENT '文章摘要',
  `category` varchar(100) DEFAULT 'general' COMMENT '文章分类',
  `tags` json DEFAULT NULL COMMENT '文章标签数组',
  `source_topic` varchar(500) DEFAULT NULL COMMENT '源话题标题',
  `keywords` json DEFAULT NULL COMMENT '关键词数组',
  `word_count` int DEFAULT 0 COMMENT '文章字数',
  `quality_score` decimal(3,2) DEFAULT 0.00 COMMENT '质量评分(0-10)',
  `status` enum('draft','published','archived','deleted') DEFAULT 'published' COMMENT '文章状态',
  `author` varchar(100) DEFAULT 'AI Generator' COMMENT '作者',
  `source_urls` json DEFAULT NULL COMMENT '参考新闻URL数组',
  `related_news_count` int DEFAULT 0 COMMENT '相关新闻数量',
  `generation_method` varchar(50) DEFAULT 'auto' COMMENT '生成方式',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `published_at` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_published_at` (`published_at`),
  KEY `idx_quality_score` (`quality_score`),
  FULLTEXT KEY `idx_title_content` (`title`,`content`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章主表';
```

### 2. topic_processing_records 表 - 话题处理记录

```sql
CREATE TABLE `topic_processing_records` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `batch_id` varchar(100) NOT NULL COMMENT '批次ID',
  `original_topic` json NOT NULL COMMENT '原始话题数据',
  `merged_topics` json DEFAULT NULL COMMENT '合并后话题数据',
  `processing_status` enum('pending','processing','completed','failed') DEFAULT 'pending' COMMENT '处理状态',
  `topics_count` int DEFAULT 0 COMMENT '话题数量',
  `merged_count` int DEFAULT 0 COMMENT '合并后数量',
  `processing_time` decimal(10,3) DEFAULT 0.000 COMMENT '处理耗时(秒)',
  `error_message` text COMMENT '错误信息',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_status` (`processing_status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='话题处理记录表';
```

### 3. news_retrieval_records 表 - 新闻检索记录

```sql
CREATE TABLE `news_retrieval_records` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `topic_id` varchar(100) NOT NULL COMMENT '话题ID',
  `search_queries` json NOT NULL COMMENT '搜索查询数组',
  `search_results` json DEFAULT NULL COMMENT '搜索结果数据',
  `total_found` int DEFAULT 0 COMMENT '找到的新闻总数',
  `relevant_count` int DEFAULT 0 COMMENT '相关新闻数量',
  `search_time` decimal(10,3) DEFAULT 0.000 COMMENT '搜索耗时(秒)',
  `similarity_threshold` decimal(3,2) DEFAULT 0.50 COMMENT '相似度阈值',
  `search_status` enum('pending','searching','completed','failed') DEFAULT 'pending' COMMENT '搜索状态',
  `error_message` text COMMENT '错误信息',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  PRIMARY KEY (`id`),
  KEY `idx_topic_id` (`topic_id`),
  KEY `idx_search_status` (`search_status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻检索记录表';
```

### 4. system_logs 表 - 系统日志

```sql
CREATE TABLE `system_logs` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `log_level` enum('DEBUG','INFO','WARNING','ERROR','CRITICAL') DEFAULT 'INFO' COMMENT '日志级别',
  `module` varchar(100) DEFAULT NULL COMMENT '模块名称',
  `operation` varchar(200) DEFAULT NULL COMMENT '操作描述',
  `message` text NOT NULL COMMENT '日志消息',
  `details` json DEFAULT NULL COMMENT '详细信息JSON',
  `execution_time` decimal(10,3) DEFAULT NULL COMMENT '执行时间(秒)',
  `user_id` varchar(100) DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_log_level` (`log_level`),
  KEY `idx_module` (`module`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_operation` (`operation`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';
```

---

## 📊 数据接口规范

### 1. 文章数据标准JSON格式

```json
{
  "id": 12345,
  "title": "特朗普关税政策引发全球关注",
  "content": "文章正文内容...",
  "summary": "文章摘要内容，通常100-200字",
  "category": "international",
  "tags": ["政治", "经济", "国际关系", "贸易"],
  "source_topic": "特朗普宣布将对泰国征收36%关税",
  "keywords": ["特朗普", "关税", "泰国", "贸易战"],
  "word_count": 1250,
  "quality_score": 8.5,
  "status": "published",
  "author": "AI Generator",
  "source_urls": [
    "https://news.example.com/article1",
    "https://news.example.com/article2"
  ],
  "related_news_count": 15,
  "generation_method": "auto",
  "created_at": "2025-07-08T21:30:00+08:00",
  "updated_at": "2025-07-08T21:30:00+08:00",
  "published_at": "2025-07-08T21:30:00+08:00"
}
```

### 2. 字段格式要求

| 字段名 | 数据类型 | 格式要求 | 示例值 |
|--------|----------|----------|--------|
| `title` | string | 10-500字符，不含特殊符号 | "特朗普关税政策引发关注" |
| `content` | string | 500-5000字符，支持Markdown | "## 背景\n\n文章内容..." |
| `summary` | string | 50-300字符 | "本文分析了..." |
| `category` | string | 预定义分类值 | "politics", "economy", "sports" |
| `tags` | array | 字符串数组，3-10个标签 | ["政治", "经济"] |
| `keywords` | array | 字符串数组，5-20个关键词 | ["特朗普", "关税"] |
| `quality_score` | number | 0.00-10.00，保留2位小数 | 8.50 |
| `status` | enum | draft/published/archived/deleted | "published" |
| `source_urls` | array | 有效URL数组 | ["https://..."] |

### 3. 文章状态枚举定义

```json
{
  "status_enum": {
    "draft": "草稿 - 文章已生成但未发布",
    "published": "已发布 - 文章正常可见",
    "archived": "已归档 - 文章不再显示但保留",
    "deleted": "已删除 - 文章被标记删除"
  }
}
```

### 4. 分类标签标准化

```json
{
  "categories": {
    "politics": "政治",
    "economy": "经济", 
    "sports": "体育",
    "technology": "科技",
    "entertainment": "娱乐",
    "society": "社会",
    "international": "国际",
    "military": "军事",
    "health": "健康",
    "education": "教育",
    "environment": "环境",
    "general": "综合"
  }
}
```

---

## 🔌 API接口文档

### 1. 获取文章列表

**接口地址**: `GET /api/articles`

**请求参数**:
```json
{
  "page": 1,
  "limit": 20,
  "category": "politics",
  "status": "published",
  "start_date": "2025-07-01",
  "end_date": "2025-07-08",
  "keyword": "特朗普",
  "sort": "created_at",
  "order": "desc"
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total": 150,
    "page": 1,
    "limit": 20,
    "pages": 8,
    "articles": [
      {
        "id": 12345,
        "title": "文章标题",
        "summary": "文章摘要",
        "category": "politics",
        "tags": ["政治", "经济"],
        "word_count": 1250,
        "quality_score": 8.5,
        "status": "published",
        "created_at": "2025-07-08T21:30:00+08:00"
      }
    ]
  }
}
```

### 2. 获取文章详情

**接口地址**: `GET /api/articles/{id}`

**响应格式**:
```json
{
  "code": 200,
  "message": "success", 
  "data": {
    // 完整文章JSON格式（见上文）
  }
}
```

### 3. 创建文章

**接口地址**: `POST /api/articles`

**请求体**:
```json
{
  "title": "文章标题",
  "content": "文章内容",
  "summary": "文章摘要",
  "category": "politics",
  "tags": ["政治", "经济"],
  "keywords": ["关键词1", "关键词2"],
  "status": "published"
}
```

### 4. 更新文章

**接口地址**: `PUT /api/articles/{id}`

### 5. 删除文章

**接口地址**: `DELETE /api/articles/{id}`

---

## 🔧 集成指南

### 1. 数据库连接配置

**Python示例**:
```python
import mysql.connector
from mysql.connector import pooling

# 连接池配置
config = {
    'user': 'root',
    'password': 'your_password',
    'host': 'localhost',
    'port': 3306,
    'database': 'news_system',
    'charset': 'utf8mb4',
    'use_unicode': True,
    'pool_name': 'news_pool',
    'pool_size': 10,
    'pool_reset_session': True
}

# 创建连接池
pool = pooling.MySQLConnectionPool(**config)

# 获取连接
def get_connection():
    return pool.get_connection()
```

**PHP示例**:
```php
<?php
$config = [
    'host' => 'localhost',
    'port' => 3306,
    'dbname' => 'news_system',
    'username' => 'root',
    'password' => 'your_password',
    'charset' => 'utf8mb4'
];

$dsn = "mysql:host={$config['host']};port={$config['port']};dbname={$config['dbname']};charset={$config['charset']}";
$pdo = new PDO($dsn, $config['username'], $config['password'], [
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
]);
?>
```

### 2. 常用SQL查询模板

**获取最新文章**:
```sql
SELECT id, title, summary, category, tags, quality_score, created_at
FROM articles 
WHERE status = 'published' 
ORDER BY created_at DESC 
LIMIT 20;
```

**按分类统计**:
```sql
SELECT category, COUNT(*) as count, AVG(quality_score) as avg_score
FROM articles 
WHERE status = 'published' 
  AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY category 
ORDER BY count DESC;
```

**全文搜索**:
```sql
SELECT id, title, summary, 
       MATCH(title, content) AGAINST(? IN NATURAL LANGUAGE MODE) as relevance
FROM articles 
WHERE status = 'published' 
  AND MATCH(title, content) AGAINST(? IN NATURAL LANGUAGE MODE)
ORDER BY relevance DESC, created_at DESC
LIMIT 20;
```

**热门标签统计**:
```sql
SELECT tag, COUNT(*) as count
FROM (
    SELECT JSON_UNQUOTE(JSON_EXTRACT(tags, CONCAT('$[', idx, ']'))) as tag
    FROM articles 
    CROSS JOIN (
        SELECT 0 as idx UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4
    ) as indices
    WHERE JSON_LENGTH(tags) > idx 
      AND status = 'published'
      AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
) as tag_list
WHERE tag IS NOT NULL
GROUP BY tag
ORDER BY count DESC
LIMIT 20;
```

### 3. 与现有系统集成建议

**数据同步策略**:
1. **实时同步**: 使用数据库触发器或消息队列
2. **定时同步**: 每小时同步一次新增文章
3. **增量同步**: 基于 `updated_at` 字段同步变更

**API集成示例**:
```python
import requests
import json

class NewsAPIClient:
    def __init__(self, base_url, api_key=None):
        self.base_url = base_url
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}' if api_key else None
        }
    
    def get_articles(self, **params):
        """获取文章列表"""
        response = requests.get(
            f"{self.base_url}/api/articles",
            params=params,
            headers=self.headers
        )
        return response.json()
    
    def get_article(self, article_id):
        """获取文章详情"""
        response = requests.get(
            f"{self.base_url}/api/articles/{article_id}",
            headers=self.headers
        )
        return response.json()
    
    def create_article(self, article_data):
        """创建文章"""
        response = requests.post(
            f"{self.base_url}/api/articles",
            json=article_data,
            headers=self.headers
        )
        return response.json()

# 使用示例
client = NewsAPIClient('http://localhost:8000')
articles = client.get_articles(category='politics', limit=10)
```

### 4. 数据同步和更新策略

**同步配置**:
```python
SYNC_CONFIG = {
    'sync_interval': 3600,  # 1小时同步一次
    'batch_size': 100,      # 每批处理100条
    'retry_times': 3,       # 失败重试3次
    'timeout': 30,          # 请求超时30秒
    'fields_mapping': {     # 字段映射
        'external_id': 'id',
        'external_title': 'title',
        'external_content': 'content'
    }
}
```

**错误处理**:
```python
def sync_articles():
    try:
        # 获取最新文章
        articles = get_latest_articles()
        
        for article in articles:
            try:
                # 数据转换
                converted_data = convert_article_format(article)
                
                # 同步到目标系统
                sync_to_target_system(converted_data)
                
            except Exception as e:
                log_sync_error(article['id'], str(e))
                
    except Exception as e:
        log_critical_error('sync_articles', str(e))
```

---

## 📚 附录

### 数据库初始化脚本
```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS news_system 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE news_system;

-- 创建所有表（见上文表结构）
-- ...

-- 插入示例数据
INSERT INTO articles (title, content, summary, category, tags, keywords) VALUES
('示例文章标题', '这是示例文章内容...', '这是示例摘要', 'general', 
 JSON_ARRAY('示例', '测试'), JSON_ARRAY('关键词1', '关键词2'));
```

### 性能优化建议
1. **索引优化**: 根据查询模式创建复合索引
2. **分区表**: 按时间分区存储历史数据
3. **缓存策略**: 使用Redis缓存热门文章
4. **读写分离**: 配置主从复制分离读写操作

### 安全注意事项
1. **SQL注入防护**: 使用参数化查询
2. **数据验证**: 严格验证输入数据格式
3. **访问控制**: 实现API认证和授权
4. **数据加密**: 敏感数据加密存储

---

**文档版本**: v1.0  
**最后更新**: 2025-07-08  
**维护者**: 集成新闻系统开发团队
