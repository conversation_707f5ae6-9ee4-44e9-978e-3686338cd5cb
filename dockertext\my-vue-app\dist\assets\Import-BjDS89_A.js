import{_ as G,v as k,c as U,a as f,b as _,e as t,f as i,w as m,g as w,r as h,d as z,t as y,F as H,m as Q,J as E,E as T,h as Y}from"./index-BpJfbBiC.js";const Z={class:"import-page"},ee={class:"page-header"},te={class:"header-actions"},se={key:0,class:"results-section"},le={class:"results-card"},ae={class:"results-header"},oe={class:"results-stats"},ne={class:"stat-item success"},ie={class:"stat-number"},re={class:"stat-item failed"},ce={class:"stat-number"},ue={class:"stat-item total"},de={class:"stat-number"},pe={key:0,class:"error-list"},me={class:"error-file"},ve={class:"error-message"},ge={class:"results-actions"},fe={key:1,class:"import-content"},_e={class:"upload-section"},he={class:"upload-card"},ye={class:"section-subtitle"},be={class:"upload-content"},we={key:0,class:"file-actions"},xe={class:"file-count"},ke={class:"settings-section"},Te={class:"settings-card"},Ce={class:"setting-group"},Ve={class:"setting-item"},Fe={class:"setting-item"},Ne={class:"setting-item"},Se={class:"setting-group"},Be={class:"setting-item"},Ie={class:"setting-item"},Le={class:"import-actions"},Pe={class:"actions-card"},Re={key:0,class:"progress-section"},Ue={class:"progress-info"},ze={class:"progress-percent"},Ee={key:1,class:"action-buttons"},$e=10*1024*1024,Me={__name:"Import",setup(je){const $=Y(),I=k(),r=k([]),V=k(!1),C=k(0),c=k({autoCategory:!0,autoTags:!0,defaultStatus:"draft",overwriteExisting:!1,batchSize:10}),F=[".txt",".md",".docx",".html"],p=k({total:0,success:0,failed:0,errors:[]}),S=k(!1),L=U(()=>r.value.length>0&&!V.value),P=U(()=>Math.round(C.value)),M=(l,e)=>{const s=l.name.toLowerCase();return F.some(u=>s.endsWith(u))?l.size>$e?(T.error("文件大小不能超过 10MB"),!1):(r.value=e,!0):(T.error(`不支持的文件类型。支持的格式：${F.join(", ")}`),!1)},j=(l,e)=>{r.value=e},B=async l=>new Promise(e=>{const s=new FileReader;s.onload=v=>{const u=v.target.result,n=u.split(`
`);let d="",a="",b="";for(let o=0;o<Math.min(5,n.length);o++){const N=n[o].trim();if(N&&!N.startsWith("话题:")&&!N.startsWith("相关新闻数:")){d=N.replace(/^#+\s*/,"");break}}let g=0;for(let o=0;o<n.length;o++)if(n[o].includes("============================================================")){g=o+1;break}g>0?a=n.slice(g).join(`
`).trim():a=u;const x=a.replace(/<[^>]*>/g,"").replace(/[#*`]/g,"");b=x.substring(0,200)+(x.length>200?"...":""),e({title:d||l.name.replace(/\.[^/.]+$/,""),content:a,summary:b,fileName:l.name,fileType:l.name.split(".").pop().toLowerCase()})},s.readAsText(l.raw,"utf-8")}),D=async l=>new Promise(e=>{const s=new FileReader;s.onload=v=>{const u=v.target.result,n=u.split(`
`);let d="";for(const g of n){const x=g.match(/^#+\s*(.+)/);if(x){d=x[1].trim();break}}const a=u.replace(/[#*`]/g,"").replace(/\[([^\]]+)\]\([^)]+\)/g,"$1"),b=a.substring(0,200)+(a.length>200?"...":"");e({title:d||l.name.replace(/\.[^/.]+$/,""),content:u,summary:b,fileName:l.name,fileType:"markdown"})},s.readAsText(l.raw,"utf-8")}),A=async l=>{const e=l.name.split(".").pop().toLowerCase();switch(e){case"txt":return await B(l);case"md":return await D(l);case"html":return await B(l);case"docx":return T.warning("DOCX文件解析功能开发中，将作为文本文件处理"),await B(l);default:throw new Error(`不支持的文件类型: ${e}`)}},O=(l,e)=>{const s=l.toLowerCase();return s.includes("nba")||s.includes("篮球")||s.includes("足球")||s.includes("体育")?"sports":s.includes("科技")||s.includes("ai")||s.includes("技术")?"technology":s.includes("经济")||s.includes("金融")||s.includes("股市")?"finance":s.includes("国际")||s.includes("外交")||s.includes("政治")?"international":s.includes("明星")||s.includes("娱乐")||s.includes("电影")?"entertainment":"social"},W=(l,e)=>{const s=[],v=l.toLowerCase(),u={NBA:["nba","篮球"],科技:["科技","ai","人工智能"],金融:["金融","经济","股市"],国际:["国际","外交"],娱乐:["娱乐","明星","电影"],社会:["社会","民生"]};for(const[n,d]of Object.entries(u))d.some(a=>v.includes(a))&&s.push(n);return s.slice(0,5)},q=async()=>{if(!L.value)return;try{await E.confirm(`确定要导入 ${r.value.length} 个文件吗？`,"确认导入",{confirmButtonText:"开始导入",cancelButtonText:"取消",type:"info"})}catch{return}V.value=!0,C.value=0,p.value={total:r.value.length,success:0,failed:0,errors:[]};const l=c.value.batchSize,e=[];for(let s=0;s<r.value.length;s+=l)e.push(r.value.slice(s,s+l));try{for(let s=0;s<e.length;s++){const u=e[s].map(async(n,d)=>{try{const a=await A(n);c.value.autoCategory&&(a.category=O(a.content,a.fileName)),c.value.autoTags&&(a.tags=W(a.content,a.fileName)),a.status=c.value.defaultStatus,await new Promise(g=>setTimeout(g,200)),p.value.success++;const b=s*l+d+1;return C.value=b/r.value.length*100,{success:!0,data:a}}catch(a){return p.value.failed++,p.value.errors.push({fileName:n.name,error:a.message}),{success:!1,error:a.message}}});await Promise.all(u),s<e.length-1&&await new Promise(n=>setTimeout(n,100))}T.success(`导入完成！成功 ${p.value.success} 个，失败 ${p.value.failed} 个`),S.value=!0}catch{T.error("导入过程中发生错误")}finally{V.value=!1,C.value=100}},R=()=>{r.value=[],I.value?.clearFiles()},J=()=>{R(),S.value=!1,C.value=0,p.value={total:0,success:0,failed:0,errors:[]}},K=()=>{$.push("/articles")},X=async()=>{try{await E.confirm("这将导入您本地的生成文章文件夹中的所有文章，确定继续吗？","快速导入",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),T.info("快速导入功能开发中，请使用文件上传方式导入")}catch{}};return(l,e)=>{const s=h("el-button"),v=h("UploadFilled"),u=h("el-icon"),n=h("el-upload"),d=h("el-checkbox"),a=h("el-option"),b=h("el-select"),g=h("el-input-number"),x=h("el-progress");return _(),f("div",Z,[t("div",ee,[e[7]||(e[7]=t("div",{class:"header-content"},[t("h1",{class:"page-title"},"文件导入"),t("p",{class:"page-subtitle"},"批量导入本地文件，快速构建您的文章库")],-1)),t("div",te,[i(s,{onClick:X,icon:l.FolderOpened},{default:m(()=>e[6]||(e[6]=[w(" 快速导入本地文章 ")])),_:1,__:[6]},8,["icon"])])]),S.value?(_(),f("div",se,[t("div",le,[t("div",ae,[e[9]||(e[9]=t("h2",{class:"results-title"},"导入结果",-1)),i(s,{onClick:J,icon:l.Refresh},{default:m(()=>e[8]||(e[8]=[w(" 重新导入 ")])),_:1,__:[8]},8,["icon"])]),t("div",oe,[t("div",ne,[t("div",ie,y(p.value.success),1),e[10]||(e[10]=t("div",{class:"stat-label"},"成功",-1))]),t("div",re,[t("div",ce,y(p.value.failed),1),e[11]||(e[11]=t("div",{class:"stat-label"},"失败",-1))]),t("div",ue,[t("div",de,y(p.value.total),1),e[12]||(e[12]=t("div",{class:"stat-label"},"总计",-1))])]),p.value.errors.length>0?(_(),f("div",pe,[e[13]||(e[13]=t("h3",{class:"error-title"},"错误详情",-1)),(_(!0),f(H,null,Q(p.value.errors,o=>(_(),f("div",{class:"error-item",key:o.fileName},[t("span",me,y(o.fileName),1),t("span",ve,y(o.error),1)]))),128))])):z("",!0),t("div",ge,[i(s,{type:"primary",onClick:K,icon:l.Document},{default:m(()=>e[14]||(e[14]=[w(" 查看导入的文章 ")])),_:1,__:[14]},8,["icon"])])])])):(_(),f("div",fe,[t("div",_e,[t("div",he,[e[17]||(e[17]=t("h2",{class:"section-title"},"选择文件",-1)),t("p",ye," 支持格式："+y(F.join(", "))+" | 单个文件最大 10MB ",1),i(n,{ref_key:"uploadRef",ref:I,"file-list":r.value,"onUpdate:fileList":e[0]||(e[0]=o=>r.value=o),"auto-upload":!1,"on-change":M,"on-remove":j,accept:F.join(","),multiple:"",drag:"",class:"upload-dragger"},{default:m(()=>[t("div",be,[i(u,{class:"upload-icon"},{default:m(()=>[i(v)]),_:1}),e[15]||(e[15]=t("div",{class:"upload-text"},[t("p",{class:"upload-title"},"拖拽文件到此处，或点击选择文件"),t("p",{class:"upload-hint"},"支持批量选择多个文件")],-1))])]),_:1},8,["file-list","accept"]),r.value.length>0?(_(),f("div",we,[i(s,{onClick:R,icon:l.Delete},{default:m(()=>e[16]||(e[16]=[w(" 清空文件 ")])),_:1,__:[16]},8,["icon"]),t("span",xe,"已选择 "+y(r.value.length)+" 个文件",1)])):z("",!0)])]),t("div",ke,[t("div",Te,[e[27]||(e[27]=t("h2",{class:"section-title"},"导入设置",-1)),t("div",Ce,[t("div",Ve,[i(d,{modelValue:c.value.autoCategory,"onUpdate:modelValue":e[1]||(e[1]=o=>c.value.autoCategory=o)},{default:m(()=>e[18]||(e[18]=[w(" 自动分类 ")])),_:1,__:[18]},8,["modelValue"]),e[19]||(e[19]=t("span",{class:"setting-desc"},"根据文章内容自动识别分类",-1))]),t("div",Fe,[i(d,{modelValue:c.value.autoTags,"onUpdate:modelValue":e[2]||(e[2]=o=>c.value.autoTags=o)},{default:m(()=>e[20]||(e[20]=[w(" 自动标签 ")])),_:1,__:[20]},8,["modelValue"]),e[21]||(e[21]=t("span",{class:"setting-desc"},"根据文章内容自动生成标签",-1))]),t("div",Ne,[i(d,{modelValue:c.value.overwriteExisting,"onUpdate:modelValue":e[3]||(e[3]=o=>c.value.overwriteExisting=o)},{default:m(()=>e[22]||(e[22]=[w(" 覆盖已存在的文章 ")])),_:1,__:[22]},8,["modelValue"]),e[23]||(e[23]=t("span",{class:"setting-desc"},"如果标题相同，覆盖原有文章",-1))])]),t("div",Se,[t("div",Be,[e[24]||(e[24]=t("label",{class:"setting-label"},"默认状态",-1)),i(b,{modelValue:c.value.defaultStatus,"onUpdate:modelValue":e[4]||(e[4]=o=>c.value.defaultStatus=o),style:{width:"150px"}},{default:m(()=>[i(a,{label:"草稿",value:"draft"}),i(a,{label:"已发布",value:"published"})]),_:1},8,["modelValue"])]),t("div",Ie,[e[25]||(e[25]=t("label",{class:"setting-label"},"批处理大小",-1)),i(g,{modelValue:c.value.batchSize,"onUpdate:modelValue":e[5]||(e[5]=o=>c.value.batchSize=o),min:1,max:50,style:{width:"150px"}},null,8,["modelValue"]),e[26]||(e[26]=t("span",{class:"setting-desc"},"每批处理的文件数量",-1))])])])]),t("div",Le,[t("div",Pe,[V.value?(_(),f("div",Re,[t("div",Ue,[e[28]||(e[28]=t("span",{class:"progress-text"},"正在导入文件...",-1)),t("span",ze,y(P.value)+"%",1)]),i(x,{percentage:P.value,"show-text":!1},null,8,["percentage"])])):(_(),f("div",Ee,[i(s,{type:"primary",size:"large",disabled:!L.value,onClick:q,icon:l.Upload},{default:m(()=>[w(" 开始导入 ("+y(r.value.length)+" 个文件) ",1)]),_:1},8,["disabled","icon"])]))])])]))])}}},Ae=G(Me,[["__scopeId","data-v-c21b697c"]]);export{Ae as default};
