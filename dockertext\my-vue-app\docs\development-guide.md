# 开发指南

本指南将帮助开发者快速上手智能文章管理系统的开发工作。

## 🏗️ 项目架构

### 技术架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    前端 (Vue 3)                             │
├─────────────────────────────────────────────────────────────┤
│  Vue Router  │  Pinia Store  │  Element Plus  │  Quill.js  │
├─────────────────────────────────────────────────────────────┤
│                    HTTP API (Axios)                        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   后端 (Express.js)                         │
├─────────────────────────────────────────────────────────────┤
│   路由层   │   控制器层   │   服务层   │   数据访问层        │
├─────────────────────────────────────────────────────────────┤
│                    MySQL 数据库                            │
└─────────────────────────────────────────────────────────────┘
```

### 目录结构详解
```
my-vue-app/
├── src/                          # 前端源码
│   ├── api/                     # API 接口层
│   │   └── articles.js          # 文章相关 API
│   ├── components/              # 公共组件
│   ├── stores/                  # Pinia 状态管理
│   │   └── articles.js          # 文章状态管理
│   ├── views/                   # 页面组件
│   │   ├── Dashboard/           # 仪表板页面
│   │   ├── ArticleList/         # 文章列表页面
│   │   ├── ArticleEdit/         # 文章编辑页面
│   │   └── Import/              # 导入页面
│   ├── router/                  # 路由配置
│   │   └── index.js             # 路由定义
│   ├── assets/                  # 静态资源
│   ├── styles/                  # 样式文件
│   └── main.js                  # 应用入口
├── dist/                        # 构建输出目录
├── docs/                        # 项目文档
├── database-server.js           # 后端服务器
├── package.json                 # 项目配置
├── vite.config.js              # Vite 配置
└── .env                        # 环境变量
```

## 🚀 开发环境搭建

### 1. 环境要求
- Node.js 16.0.0+
- MySQL 8.0+
- Git
- VS Code (推荐)

### 2. 克隆项目
```bash
git clone <repository-url>
cd my-vue-app
```

### 3. 安装依赖
```bash
npm install
```

### 4. 配置数据库
```sql
-- 创建开发数据库
CREATE DATABASE news_system_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建测试数据库
CREATE DATABASE news_system_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 5. 环境变量配置
```bash
# 开发环境 .env
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=news_system_dev
PORT=9486
NODE_ENV=development

# 前端环境变量
VITE_API_BASE_URL=http://localhost:9486/api
```

### 6. 启动开发服务器
```bash
# 启动后端 (终端1)
node database-server.js

# 启动前端 (终端2)
npm run dev
```

## 📝 开发规范

### 代码风格
- 使用 ESLint + Prettier 进行代码格式化
- 遵循 Vue 3 Composition API 最佳实践
- 使用 TypeScript 类型注释（推荐）

### 命名规范
```javascript
// 组件命名：PascalCase
const ArticleList = defineComponent({})

// 变量命名：camelCase
const articleList = ref([])

// 常量命名：UPPER_SNAKE_CASE
const API_BASE_URL = 'http://localhost:9486/api'

// 文件命名：kebab-case
// article-list.vue, user-profile.js
```

### Git 提交规范
```bash
# 格式：type(scope): description

# 类型说明：
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建工具、依赖更新

# 示例：
git commit -m "feat(article): 添加文章自动保存功能"
git commit -m "fix(api): 修复文章删除接口错误"
git commit -m "docs: 更新API文档"
```

## 🔧 核心功能开发

### 1. 添加新的API接口

#### 后端开发
```javascript
// database-server.js
app.get('/api/new-endpoint', async (req, res) => {
  try {
    // 业务逻辑
    const result = await someOperation()
    
    res.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('错误:', error)
    res.status(500).json({
      success: false,
      error: error.message
    })
  }
})
```

#### 前端API调用
```javascript
// src/api/articles.js
export const newApiCall = async (params) => {
  try {
    const response = await api.get('/new-endpoint', { params })
    return response.data
  } catch (error) {
    console.error('API调用失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
```

### 2. 添加新页面

#### 创建页面组件
```vue
<!-- src/views/NewPage/NewPage.vue -->
<script setup>
import { ref, onMounted } from 'vue'

const data = ref([])
const loading = ref(false)

const loadData = async () => {
  loading.value = true
  try {
    // 加载数据逻辑
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>

<template>
  <div class="new-page">
    <h1>新页面</h1>
    <!-- 页面内容 -->
  </div>
</template>

<style scoped>
.new-page {
  padding: var(--spacing-xl);
}
</style>
```

#### 添加路由
```javascript
// src/router/index.js
{
  path: '/new-page',
  name: 'NewPage',
  component: () => import('../views/NewPage/NewPage.vue')
}
```

### 3. 状态管理

#### 创建Store
```javascript
// src/stores/newStore.js
import { defineStore } from 'pinia'

export const useNewStore = defineStore('newStore', {
  state: () => ({
    data: [],
    loading: false
  }),
  
  getters: {
    filteredData: (state) => {
      return state.data.filter(item => item.active)
    }
  },
  
  actions: {
    async fetchData() {
      this.loading = true
      try {
        const response = await api.getData()
        this.data = response.data
      } finally {
        this.loading = false
      }
    }
  }
})
```

## 🧪 测试指南

### 单元测试
```javascript
// tests/unit/component.test.js
import { mount } from '@vue/test-utils'
import Component from '@/components/Component.vue'

describe('Component', () => {
  it('renders correctly', () => {
    const wrapper = mount(Component)
    expect(wrapper.text()).toContain('Expected Text')
  })
})
```

### API测试
```javascript
// tests/api/articles.test.js
import { describe, it, expect } from 'vitest'
import { getArticles } from '@/api/articles'

describe('Articles API', () => {
  it('should fetch articles', async () => {
    const result = await getArticles()
    expect(result.success).toBe(true)
    expect(Array.isArray(result.data)).toBe(true)
  })
})
```

### 运行测试
```bash
# 运行所有测试
npm run test

# 运行单元测试
npm run test:unit

# 运行E2E测试
npm run test:e2e

# 测试覆盖率
npm run test:coverage
```

## 🔍 调试技巧

### 前端调试
```javascript
// 使用 Vue DevTools
// 在组件中添加调试信息
console.log('组件数据:', toRaw(data.value))

// 使用 debugger
const handleClick = () => {
  debugger // 浏览器会在此处暂停
  // 处理逻辑
}
```

### 后端调试
```javascript
// 添加详细日志
console.log('[API] 请求参数:', req.params, req.query, req.body)
console.log('[DB] 查询结果:', result)

// 使用 Node.js 调试器
node --inspect database-server.js
```

### 数据库调试
```sql
-- 查看慢查询
SHOW PROCESSLIST;

-- 分析查询性能
EXPLAIN SELECT * FROM articles WHERE title LIKE '%keyword%';

-- 查看表结构
DESCRIBE articles;
```

## 📦 构建和部署

### 开发构建
```bash
# 开发模式构建
npm run build:dev

# 生产模式构建
npm run build

# 预览构建结果
npm run preview
```

### 环境配置
```javascript
// vite.config.js
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['element-plus']
        }
      }
    }
  }
})
```

## 🔧 常用工具和插件

### VS Code 扩展推荐
- Vue - Official (Vue 3 支持)
- ESLint (代码检查)
- Prettier (代码格式化)
- Auto Rename Tag (标签重命名)
- Bracket Pair Colorizer (括号高亮)

### 开发工具
```bash
# 代码格式化
npm run lint
npm run format

# 类型检查
npm run type-check

# 依赖分析
npm run analyze
```

## 📚 学习资源

### 官方文档
- [Vue 3 文档](https://vuejs.org/)
- [Vite 文档](https://vitejs.dev/)
- [Element Plus 文档](https://element-plus.org/)
- [Pinia 文档](https://pinia.vuejs.org/)

### 最佳实践
- [Vue 3 最佳实践](https://vuejs.org/guide/best-practices/)
- [JavaScript 代码规范](https://standardjs.com/)
- [Git 工作流](https://www.atlassian.com/git/tutorials/comparing-workflows)

## ❓ 常见问题

### Q: 如何添加新的文章分类？
A: 在前端的分类选项中添加新分类，后端会自动支持。

### Q: 如何修改数据库表结构？
A: 使用 MySQL ALTER TABLE 语句，注意备份数据。

### Q: 如何优化页面加载速度？
A: 使用懒加载、代码分割、图片优化等技术。

### Q: 如何处理跨域问题？
A: 在开发环境中配置 Vite 代理，生产环境使用 Nginx。

---

**开发愉快！如有问题，请查看相关文档或联系团队成员。**
