<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const loading = ref(true)

onMounted(() => {
  // 模拟加载
  setTimeout(() => {
    loading.value = false
  }, 500)
})
</script>

<template>
  <div class="app-container">
    <!-- 加载动画 -->
    <div v-if="loading" class="loading-screen">
      <div class="loading-spinner">
        <div class="spinner"></div>
        <p>智能笔记系统启动中...</p>
      </div>
    </div>

    <!-- 主应用 -->
    <div v-else class="main-app">
      <!-- 侧边栏 -->
      <aside class="sidebar">
        <div class="sidebar-header">
          <div class="logo">
            <el-icon class="logo-icon"><Document /></el-icon>
            <span class="logo-text">智能笔记</span>
          </div>
        </div>

        <nav class="sidebar-nav">
          <div class="nav-section">
            <h3 class="nav-title">主要功能</h3>
            <ul class="nav-list">
              <li class="nav-item">
                <router-link to="/dashboard" class="nav-link">
                  <el-icon><Odometer /></el-icon>
                  <span>仪表板</span>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/articles" class="nav-link">
                  <el-icon><Document /></el-icon>
                  <span>文章管理</span>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/article/edit" class="nav-link">
                  <el-icon><Edit /></el-icon>
                  <span>创建文章</span>
                </router-link>
              </li>
              <li class="nav-item">
                <router-link to="/import" class="nav-link">
                  <el-icon><Upload /></el-icon>
                  <span>导入文件</span>
                </router-link>
              </li>
            </ul>
          </div>
        </nav>
      </aside>

      <!-- 主内容区 -->
      <main class="main-content">
        <router-view />
      </main>
    </div>
  </div>
</template>

<style scoped>
.app-container {
  height: 100vh;
  overflow: hidden;
}

.loading-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: var(--bg-primary);
}

.loading-spinner {
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner p {
  color: var(--text-secondary);
  font-size: 14px;
}

.main-app {
  display: flex;
  height: 100vh;
}

.sidebar {
  width: 260px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.logo-icon {
  font-size: 24px;
  color: var(--primary-color);
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.sidebar-nav {
  flex: 1;
  padding: var(--spacing-lg);
}

.nav-section {
  margin-bottom: var(--spacing-xl);
}

.nav-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-md);
}

.nav-list {
  list-style: none;
}

.nav-item {
  margin-bottom: var(--spacing-xs);
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.nav-link:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transform: translateX(2px);
}

.nav-link.router-link-active {
  background: var(--primary-color);
  color: white;
}

.nav-link.router-link-active:hover {
  background: var(--primary-dark);
}

.main-content {
  flex: 1;
  overflow: auto;
  background: var(--bg-primary);
}

@media (max-width: 768px) {
  .sidebar {
    width: 200px;
  }

  .logo-text {
    display: none;
  }
}
</style>
