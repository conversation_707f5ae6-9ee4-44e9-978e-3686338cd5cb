<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { useArticlesStore } from '../../stores/articles.js'
import { saveArticleToDatabase, createArticle } from '../../api/articles.js'
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import { ArrowLeft, View, Document, Check, Save, Upload, Picture, Timer, Eye, Hide, ArrowDown } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 文章数据
const article = ref({
  id: null,
  title: '',
  content: '',
  summary: '',
  status: 'draft',
  category: '',
  tags: [],
  createdAt: null,
  updatedAt: null
})

const loading = ref(false)
const saving = ref(false)
const isEditing = ref(false)

// 新增功能状态
const autoSaving = ref(false)
const lastSaved = ref(null)
const hasUnsavedChanges = ref(false)
const previewMode = ref(false)
const wordCount = ref(0)
const characterCount = ref(0)
const readingTime = ref(0)
const autoSaveTimer = ref(null)
const tagInput = ref('')
const availableTags = ref(['时政', '娱乐', '科技', '体育', '社会', '国际', '财经', '其他'])
const filteredTags = ref([])

// 文章模板
const templates = ref([
  {
    name: '新闻报道',
    title: '新闻标题',
    content: '<h2>导语</h2><p>简要概述新闻要点...</p><h2>正文</h2><p>详细报道内容...</p><h2>结语</h2><p>总结或展望...</p>'
  },
  {
    name: '观点评论',
    title: '评论标题',
    content: '<h2>引言</h2><p>引出话题...</p><h2>观点阐述</h2><p>详细论述观点...</p><h2>总结</h2><p>总结观点...</p>'
  },
  {
    name: '深度分析',
    title: '分析标题',
    content: '<h2>背景介绍</h2><p>介绍相关背景...</p><h2>问题分析</h2><p>深入分析问题...</p><h2>解决方案</h2><p>提出解决方案...</p>'
  }
])

// 编辑器配置
const editorOptions = {
  theme: 'snow',
  placeholder: '开始写作您的文章...',
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ 'header': 1 }, { 'header': 2 }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'size': ['small', false, 'large', 'huge'] }],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'align': [] }],
      ['clean'],
      ['link', 'image']
    ]
  }
}

// 分类选项
const categoryOptions = [
  { label: '时政', value: '时政' },
  { label: '娱乐', value: '娱乐' },
  { label: '科技', value: '科技' },
  { label: '体育', value: '体育' },
  { label: '社会', value: '社会' },
  { label: '国际', value: '国际' },
  { label: '财经', value: '财经' },
  { label: '其他', value: '其他' }
]

const inputVisible = ref(false)

// 计算属性
const pageTitle = computed(() => {
  return isEditing.value ? `编辑文章 - ${article.value.title || '无标题'}` : '创建新文章'
})

const canSave = computed(() => {
  return article.value.title.trim() && article.value.content.trim() && !saving.value
})

const autoSaveStatus = computed(() => {
  if (autoSaving.value) return '正在自动保存...'
  if (lastSaved.value) return `上次保存: ${lastSaved.value.toLocaleTimeString()}`
  return '未保存'
})

// 新增方法
const updateWordCount = () => {
  const text = article.value.content.replace(/<[^>]*>/g, '').trim()
  wordCount.value = text.length
  characterCount.value = text.replace(/\s/g, '').length
  readingTime.value = Math.ceil(wordCount.value / 200) // 假设每分钟阅读200字
}

const startAutoSave = () => {
  if (autoSaveTimer.value) {
    clearInterval(autoSaveTimer.value)
  }
  autoSaveTimer.value = setInterval(async () => {
    if (hasUnsavedChanges.value && canSave.value) {
      await autoSave()
    }
  }, 30000) // 每30秒自动保存
}

const autoSave = async () => {
  if (!canSave.value) return

  autoSaving.value = true
  try {
    await saveArticle(false) // 静默保存
    lastSaved.value = new Date()
    hasUnsavedChanges.value = false
  } catch (error) {
    console.error('自动保存失败:', error)
  } finally {
    autoSaving.value = false
  }
}

const applyTemplate = (template) => {
  article.value.title = template.title
  article.value.content = template.content
  hasUnsavedChanges.value = true
  updateWordCount()
  ElMessage.success(`已应用模板: ${template.name}`)
}

const togglePreview = () => {
  previewMode.value = !previewMode.value
}

const filterTags = (query) => {
  if (query) {
    filteredTags.value = availableTags.value.filter(tag =>
      tag.toLowerCase().includes(query.toLowerCase())
    )
  } else {
    filteredTags.value = availableTags.value
  }
}

const addTag = () => {
  const tag = tagInput.value.trim()
  if (tag && !article.value.tags.includes(tag)) {
    article.value.tags.push(tag)
    tagInput.value = ''
    hasUnsavedChanges.value = true
  }
}

const removeTag = (tag) => {
  const index = article.value.tags.indexOf(tag)
  if (index > -1) {
    article.value.tags.splice(index, 1)
    hasUnsavedChanges.value = true
  }
}

// 方法
const loadArticle = async (id) => {
  if (!id) return

  loading.value = true
  try {
    // 从store中查找文章基本信息
    const articlesStore = useArticlesStore()
    const foundArticle = articlesStore.articles.find(a => a.id == id)

    if (foundArticle) {
      // 优先使用store中已编辑的内容
      if (foundArticle.content && foundArticle.content !== foundArticle.fullSummary) {
        // 如果store中有编辑过的内容，直接使用
        article.value = {
          id: foundArticle.id,
          title: foundArticle.title,
          content: foundArticle.content,
          summary: foundArticle.summary,
          status: foundArticle.status || 'draft',
          category: foundArticle.category || '',
          tags: foundArticle.tags || [],
          createdAt: foundArticle.createdAt,
          updatedAt: foundArticle.updatedAt
        }
      } else {
        // 如果没有编辑过的内容，从数据库加载
        const fullArticleData = await articlesStore.loadArticle(foundArticle.id)

        if (fullArticleData) {
          article.value = {
            id: foundArticle.id,
            title: fullArticleData.title || foundArticle.title,
            content: fullArticleData.content || '开始编写您的文章内容...',
            summary: fullArticleData.news_points || fullArticleData.summary || foundArticle.summary,
            status: fullArticleData.status || foundArticle.status || 'draft',
            category: foundArticle.category || '',
            tags: foundArticle.tags || [],
            createdAt: foundArticle.createdAt,
            updatedAt: foundArticle.updatedAt
          }
        } else {
          // 如果API加载失败，使用store中的数据
          article.value = {
            ...foundArticle,
            content: foundArticle.content || '开始编写您的文章内容...'
          }
        }
      }
      isEditing.value = true
    } else {
      ElMessage.error('文章不存在')
      handleBack()
    }
  } catch (error) {
    console.error('加载文章失败:', error)
    ElMessage.error('加载文章失败')
    handleBack()
  } finally {
    loading.value = false
  }
}

const saveArticle = async (status = 'draft') => {
  if (!article.value.title.trim()) {
    ElMessage.warning('请输入文章标题')
    return
  }

  if (!article.value.content.trim()) {
    ElMessage.warning('请输入文章内容')
    return
  }

  saving.value = true
  try {
    const articlesStore = useArticlesStore()

    // 更新文章数据
    article.value.status = status
    article.value.updatedAt = new Date().toLocaleString('zh-CN')

    if (!isEditing.value) {
      // 新文章 - 保存到数据库
      console.log('创建新文章到数据库')
      const createResult = await createArticle({
        topic: article.value.title || '新文章',
        title: article.value.title,
        content: article.value.content,
        news_points: article.value.summary,
        status: article.value.status
      })

      if (createResult.success) {
        article.value.id = createResult.data.id
        article.value.createdAt = article.value.updatedAt
        isEditing.value = true
        console.log('新文章创建成功，ID:', article.value.id)

        // 更新store
        articlesStore.addArticle({
          ...article.value,
          fileName: `article_${article.value.id}.txt`
        })
      } else {
        throw new Error(createResult.error)
      }
    } else {
      // 更新现有文章 - 先更新store
      articlesStore.updateArticle(article.value.id, {
        title: article.value.title,
        content: article.value.content,
        summary: article.value.summary,
        status: article.value.status,
        category: article.value.category,
        tags: article.value.tags,
        updatedAt: article.value.updatedAt
      })

      // 然后保存到数据库
      console.log('准备保存到数据库，文章ID:', article.value.id)
      const saveResult = await saveArticleToDatabase(article.value.id, {
        title: article.value.title,
        content: article.value.content,
        news_points: article.value.summary,
        status: article.value.status
      })

      console.log('保存结果:', saveResult)
      if (!saveResult.success) {
        throw new Error(saveResult.error)
      } else {
        console.log('数据库保存成功!')
      }
    }

    const statusText = status === 'published' ? '发布' : '保存'
    ElMessage.success(`文章${statusText}成功`)

    if (status === 'published') {
      handleBack()
    }
  } catch (error) {
    console.error('保存文章失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

const handleBack = () => {
  // 检查是否有返回查询参数
  const returnQuery = route.query.returnQuery
  if (returnQuery) {
    try {
      const parsedQuery = JSON.parse(returnQuery)
      router.push({ path: '/articles', query: parsedQuery })
    } catch (error) {
      console.error('解析返回查询参数失败:', error)
      router.push('/articles')
    }
  } else {
    router.push('/articles')
  }
}

const generateSummary = () => {
  if (!article.value.content) {
    ElMessage.warning('请先输入文章内容')
    return
  }

  // 简单的摘要生成逻辑
  const textContent = article.value.content.replace(/<[^>]*>/g, '')
  const sentences = textContent.split(/[。！？]/).filter(s => s.trim().length > 10)
  article.value.summary = sentences.slice(0, 2).join('。') + '。'

  ElMessage.success('摘要生成成功')
}

const handleTagClose = (tag) => {
  article.value.tags.splice(article.value.tags.indexOf(tag), 1)
}

const showTagInput = () => {
  inputVisible.value = true
}

const handleTagInputConfirm = () => {
  if (tagInput.value && !article.value.tags.includes(tagInput.value)) {
    article.value.tags.push(tagInput.value)
  }
  inputVisible.value = false
  tagInput.value = ''
}

const previewArticle = () => {
  // 这里可以打开预览窗口或跳转到预览页面
  ElMessage.info('预览功能开发中...')
}

// 键盘快捷键
const handleKeydown = (event) => {
  // Ctrl+S 保存
  if (event.ctrlKey && event.key === 's') {
    event.preventDefault()
    saveArticle('draft')
    // 显示保存提示
    ElMessage.success('文章已保存 (Ctrl+S)')
  }
}

// 监听器
watch(() => article.value.content, () => {
  hasUnsavedChanges.value = true
  updateWordCount()
}, { deep: true })

watch(() => article.value.title, () => {
  hasUnsavedChanges.value = true
})

watch(() => tagInput.value, (newVal) => {
  filterTags(newVal)
})

// 路由离开确认
onBeforeRouteLeave((_to, _from, next) => {
  if (hasUnsavedChanges.value) {
    ElMessageBox.confirm(
      '您有未保存的更改，确定要离开吗？',
      '确认离开',
      {
        confirmButtonText: '离开',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      next()
    }).catch(() => {
      next(false)
    })
  } else {
    next()
  }
})

onMounted(() => {
  const articleId = route.params.id
  if (articleId && articleId !== 'new') {
    loadArticle(articleId)
  } else {
    // 新文章的默认内容
    article.value.content = '<p>开始编写您的文章内容...</p>'
    updateWordCount()
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)

  // 启动自动保存
  startAutoSave()

  // 初始化标签过滤
  filterTags('')
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
  if (autoSaveTimer.value) {
    clearInterval(autoSaveTimer.value)
  }
})
</script>

<template>
  <div class="article-edit">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 编辑器 -->
    <div v-else class="editor-container">
      <!-- 顶部工具栏 -->
      <div class="editor-header">
        <div class="header-left">
          <el-button :icon="ArrowLeft" @click="handleBack" size="large">
            返回
          </el-button>
          <div class="title-section">
            <h1 class="page-title">{{ pageTitle }}</h1>
            <div class="status-info">
              <span class="auto-save-status">{{ autoSaveStatus }}</span>
              <el-tag v-if="hasUnsavedChanges" type="warning" size="small">未保存</el-tag>
            </div>
          </div>
        </div>

        <div class="header-right">
          <!-- 模板选择 -->
          <el-dropdown trigger="click" @command="applyTemplate">
            <el-button :icon="Document" size="large">
              模板 <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  v-for="template in templates"
                  :key="template.name"
                  :command="template"
                >
                  {{ template.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <!-- 预览切换 -->
          <el-button
            @click="togglePreview"
            :icon="previewMode ? Hide : Eye"
            :type="previewMode ? 'primary' : 'default'"
            size="large"
          >
            {{ previewMode ? '编辑' : '预览' }}
          </el-button>

          <!-- 保存按钮组 -->
          <el-button-group>
            <el-tooltip content="快捷键: Ctrl+S" placement="bottom">
              <el-button
                @click="saveArticle('draft')"
                :loading="saving || autoSaving"
                :icon="Save"
                size="large"
                :disabled="!canSave"
              >
                保存草稿
              </el-button>
            </el-tooltip>
            <el-button
              type="primary"
              @click="saveArticle('published')"
              :loading="saving"
              :icon="Check"
              size="large"
              :disabled="!canSave"
            >
              发布文章
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 统计信息栏 -->
      <div class="stats-bar">
        <div class="stats-item">
          <el-icon><Timer /></el-icon>
          <span>字数: {{ wordCount }}</span>
        </div>
        <div class="stats-item">
          <el-icon><View /></el-icon>
          <span>预计阅读: {{ readingTime }}分钟</span>
        </div>
        <div class="stats-item">
          <el-icon><Document /></el-icon>
          <span>字符: {{ characterCount }}</span>
        </div>
      </div>

      <!-- 主编辑区域 -->
      <div class="editor-content">
        <!-- 编辑模式 -->
        <div v-if="!previewMode" class="edit-mode">
          <div class="editor-main">
            <!-- 标题输入 -->
            <div class="title-section">
              <el-input
                v-model="article.title"
                placeholder="请输入文章标题..."
                size="large"
                class="title-input"
                maxlength="100"
                show-word-limit
              />
            </div>

            <!-- 富文本编辑器 -->
            <div class="content-section">
              <QuillEditor
                :content="article.content"
                :options="editorOptions"
                contentType="html"
                class="quill-editor"
                @update:content="(content) => article.content = content"
              />
            </div>
          </div>
        </div>

        <!-- 预览模式 -->
        <div v-else class="preview-mode">
          <div class="preview-container">
            <h1 class="preview-title">{{ article.title || '无标题' }}</h1>
            <div class="preview-meta">
              <span>分类: {{ article.category || '未分类' }}</span>
              <span>状态: {{ article.status === 'draft' ? '草稿' : '已发布' }}</span>
              <span>字数: {{ wordCount }}</span>
            </div>
            <div class="preview-content" v-html="article.content"></div>
          </div>
        </div>

        <!-- 右侧设置面板 -->
        <div class="editor-sidebar">
          <!-- 文章统计 -->
          <div class="setting-card">
            <h3 class="card-title">
              <el-icon><Timer /></el-icon>
              文章统计
            </h3>
            <div class="stats-grid">
              <div class="stat-item">
                <span class="stat-label">字数</span>
                <span class="stat-value">{{ wordCount }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">字符</span>
                <span class="stat-value">{{ characterCount }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">阅读时间</span>
                <span class="stat-value">{{ readingTime }}分钟</span>
              </div>
            </div>
          </div>

          <!-- 文章设置 -->
          <div class="setting-card">
            <h3 class="card-title">
              <el-icon><Document /></el-icon>
              文章设置
            </h3>

            <div class="setting-item">
              <label class="setting-label">状态</label>
              <el-select v-model="article.status" style="width: 100%" size="large">
                <el-option label="草稿" value="draft" />
                <el-option label="已发布" value="published" />
                <el-option label="已归档" value="archived" />
              </el-select>
            </div>

            <div class="setting-item">
              <label class="setting-label">分类</label>
              <el-select v-model="article.category" placeholder="选择分类" style="width: 100%" size="large">
                <el-option
                  v-for="option in categoryOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </div>
            
            <div class="setting-item">
              <label class="setting-label">标签</label>
              <div class="tags-container">
                <el-tag
                  v-for="tag in article.tags"
                  :key="tag"
                  closable
                  @close="removeTag(tag)"
                  class="tag-item"
                  size="large"
                >
                  {{ tag }}
                </el-tag>
              </div>

              <!-- 标签输入 -->
              <div class="tag-input-section">
                <el-autocomplete
                  v-model="tagInput"
                  :fetch-suggestions="(query, cb) => cb(filteredTags.map(tag => ({ value: tag })))"
                  placeholder="输入标签名称"
                  @keyup.enter="addTag"
                  @select="(item) => { tagInput = item.value; addTag() }"
                  style="width: 100%"
                  size="large"
                >
                  <template #append>
                    <el-button @click="addTag" :icon="Check">添加</el-button>
                  </template>
                </el-autocomplete>
              </div>
            </div>

            <!-- 摘要 -->
            <div class="setting-item">
              <label class="setting-label">文章摘要</label>
              <el-input
                v-model="article.summary"
                type="textarea"
                :rows="4"
                placeholder="请输入文章摘要..."
                maxlength="200"
                show-word-limit
                resize="none"
              />
            </div>
          </div>

          <!-- 文章信息 -->
          <div v-if="isEditing" class="setting-card">
            <h3 class="card-title">
              <el-icon><View /></el-icon>
              文章信息
            </h3>

            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">创建时间</span>
                <span class="info-value">{{ new Date(article.createdAt).toLocaleString() }}</span>
              </div>

              <div class="info-item">
                <span class="info-label">更新时间</span>
                <span class="info-value">{{ new Date(article.updatedAt).toLocaleString() }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 主容器 */
.article-edit {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fafbfc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.loading-container {
  padding: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 顶部工具栏 */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: white;
  border-bottom: 1px solid #e1e5e9;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.title-section {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.auto-save-status {
  font-size: 13px;
  color: #6b7280;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* 统计信息栏 */
.stats-bar {
  display: flex;
  align-items: center;
  gap: 2rem;
  padding: 0.75rem 2rem;
  background: #f8fafc;
  border-bottom: 1px solid #e1e5e9;
  font-size: 14px;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #4b5563;
}

.stats-item .el-icon {
  color: #6b7280;
}

/* 主编辑区域 */
.editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.edit-mode {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.editor-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 2rem;
  overflow: hidden;
}

.title-section {
  margin-bottom: 1.5rem;
}

.title-input {
  font-size: 24px;
  font-weight: 600;
}

.title-input :deep(.el-input__inner) {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.3;
  padding: 1rem 1.25rem;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.title-input :deep(.el-input__inner:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 富文本编辑器优化 */
.quill-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  overflow: hidden;
  background: white;
}

.quill-editor :deep(.ql-toolbar) {
  border: none;
  border-bottom: 1px solid #e1e5e9;
  padding: 1rem;
  background: #f8fafc;
}

.quill-editor :deep(.ql-container) {
  flex: 1;
  border: none;
  font-size: 16px;
  line-height: 1.7;
}

.quill-editor :deep(.ql-editor) {
  padding: 1.5rem;
  font-size: 16px;
  line-height: 1.7;
  color: #1f2937;
}

.quill-editor :deep(.ql-editor h1) {
  font-size: 28px;
  font-weight: 700;
  margin: 1.5rem 0 1rem 0;
  color: #111827;
}

.quill-editor :deep(.ql-editor h2) {
  font-size: 24px;
  font-weight: 600;
  margin: 1.25rem 0 0.75rem 0;
  color: #111827;
}

.quill-editor :deep(.ql-editor p) {
  margin: 0.75rem 0;
  font-size: 16px;
  line-height: 1.7;
}

/* 预览模式 */
.preview-mode {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: white;
}

.preview-container {
  max-width: 800px;
  margin: 0 auto;
}

.preview-title {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 1rem 0;
  line-height: 1.3;
}

.preview-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e1e5e9;
  font-size: 14px;
  color: #6b7280;
}

.preview-content {
  font-size: 18px;
  line-height: 1.8;
  color: #1f2937;
}

.preview-content :deep(h1) {
  font-size: 28px;
  font-weight: 700;
  margin: 2rem 0 1rem 0;
  color: #111827;
}

.preview-content :deep(h2) {
  font-size: 24px;
  font-weight: 600;
  margin: 1.5rem 0 0.75rem 0;
  color: #111827;
}

.preview-content :deep(p) {
  margin: 1rem 0;
  line-height: 1.8;
}

/* 侧边栏 */
.editor-sidebar {
  width: 320px;
  background: white;
  border-left: 1px solid #e1e5e9;
  overflow-y: auto;
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.setting-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-title .el-icon {
  color: #6b7280;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.stat-item {
  text-align: center;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.stat-value {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

/* 设置项 */
.setting-item {
  margin-bottom: 1.5rem;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

/* 标签容器 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.tag-item {
  font-size: 13px;
  border-radius: 6px;
}

.tag-input-section {
  margin-top: 0.5rem;
}

/* 信息网格 */
.info-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}

.info-value {
  font-size: 13px;
  color: #111827;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .editor-sidebar {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .editor-content {
    flex-direction: column;
  }

  .editor-sidebar {
    width: 100%;
    border-left: none;
    border-top: 1px solid #e1e5e9;
    max-height: 300px;
  }

  .editor-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-left,
  .header-right {
    justify-content: center;
  }

  .stats-bar {
    flex-wrap: wrap;
    gap: 1rem;
  }

  .editor-main {
    padding: 1rem;
  }

  .title-input :deep(.el-input__inner) {
    font-size: 20px;
  }

  .preview-title {
    font-size: 24px;
  }

  .preview-content {
    font-size: 16px;
  }
}

/* 加载和保存状态 */
.el-button.is-loading {
  opacity: 0.7;
}

/* 自定义滚动条 */
.editor-sidebar::-webkit-scrollbar,
.preview-mode::-webkit-scrollbar {
  width: 6px;
}

.editor-sidebar::-webkit-scrollbar-track,
.preview-mode::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.editor-sidebar::-webkit-scrollbar-thumb,
.preview-mode::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.editor-sidebar::-webkit-scrollbar-thumb:hover,
.preview-mode::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 动画效果 */
.setting-card {
  transition: all 0.2s ease;
}

.setting-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.el-button {
  transition: all 0.2s ease;
}

.stats-item {
  transition: all 0.2s ease;
}

.stats-item:hover {
  background: #f1f5f9;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.editor-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.toolbar-right {
  display: flex;
  gap: var(--spacing-sm);
}

.editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.editor-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.title-section {
  padding: var(--spacing-xl);
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-color);
}

.title-input {
  font-size: 24px;
  font-weight: 600;
}

.title-input :deep(.el-input__wrapper) {
  background: transparent;
  border: none;
  box-shadow: none;
  padding: 0;
}

.title-input :deep(.el-input__inner) {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
}

.content-section {
  flex: 1;
  overflow: auto;
  max-height: calc(100vh - 200px);
}

.quill-editor {
  height: calc(100vh - 280px);
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  background: white;
}

.quill-editor :deep(.ql-container) {
  height: calc(100% - 42px);
  font-size: 16px;
  line-height: 1.6;
  overflow-y: auto;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.quill-editor :deep(.ql-editor) {
  padding: 20px;
  color: #303133;
  min-height: 400px;
}

.quill-editor :deep(.ql-toolbar) {
  border-bottom: 1px solid #dcdfe6;
  background: #f5f7fa;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.quill-editor :deep(.ql-editor.ql-blank::before) {
  color: #c0c4cc;
  font-style: normal;
}

.editor-sidebar {
  width: 320px;
  background: var(--bg-secondary);
  border-left: 1px solid var(--border-color);
  overflow-y: auto;
  padding: var(--spacing-xl);
}

.setting-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.setting-item {
  margin-bottom: var(--spacing-md);
}

.setting-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  align-items: center;
}

.tag-item {
  margin: 0;
}

.tag-input {
  width: 80px;
}

.add-tag-btn {
  border-style: dashed;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
  font-size: 14px;
}

.info-label {
  color: var(--text-secondary);
}

.info-value {
  color: var(--text-primary);
}

@media (max-width: 768px) {
  .editor-content {
    flex-direction: column;
  }
  
  .editor-sidebar {
    width: 100%;
    max-height: 300px;
  }
  
  .toolbar-right {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}
</style>
