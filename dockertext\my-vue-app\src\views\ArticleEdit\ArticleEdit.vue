<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useArticlesStore } from '../../stores/articles.js'
import { saveArticleToDatabase, createArticle } from '../../api/articles.js'
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'
import { ArrowLeft, View, Document, Check } from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()

// 文章数据
const article = ref({
  id: null,
  title: '',
  content: '',
  summary: '',
  status: 'draft',
  category: '',
  tags: [],
  createdAt: null,
  updatedAt: null
})

const loading = ref(false)
const saving = ref(false)
const isEditing = ref(false)
const autoSaving = ref(false)
const lastSaved = ref(null)
const hasUnsavedChanges = ref(false)

// 字数统计
const wordCount = computed(() => {
  if (!article.value.content) return 0
  // 移除HTML标签并计算字数
  const text = article.value.content.replace(/<[^>]*>/g, '').trim()
  return text.length
})

// 自动保存定时器
let autoSaveTimer = null
const AUTO_SAVE_INTERVAL = 30000 // 30秒自动保存

// 编辑器配置
const editorOptions = {
  theme: 'snow',
  placeholder: '开始写作您的文章...',
  modules: {
    toolbar: [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ 'header': 1 }, { 'header': 2 }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'size': ['small', false, 'large', 'huge'] }],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'align': [] }],
      ['clean'],
      ['link', 'image']
    ]
  },
  // 优化编辑器性能
  bounds: document.body,
  scrollingContainer: null,
  strict: false
}

// 分类选项
const categoryOptions = [
  { label: '时政', value: '时政' },
  { label: '娱乐', value: '娱乐' },
  { label: '科技', value: '科技' },
  { label: '体育', value: '体育' },
  { label: '社会', value: '社会' },
  { label: '国际', value: '国际' },
  { label: '财经', value: '财经' },
  { label: '其他', value: '其他' }
]

// 自动保存功能
const startAutoSave = () => {
  if (autoSaveTimer) {
    clearInterval(autoSaveTimer)
  }
  autoSaveTimer = setInterval(async () => {
    if (hasUnsavedChanges.value && !saving.value) {
      await autoSave()
    }
  }, AUTO_SAVE_INTERVAL)
}

const stopAutoSave = () => {
  if (autoSaveTimer) {
    clearInterval(autoSaveTimer)
    autoSaveTimer = null
  }
}

const autoSave = async () => {
  if (!article.value.title.trim() && !article.value.content.trim()) {
    return // 空文章不自动保存
  }

  try {
    autoSaving.value = true
    await saveArticle(false) // false表示自动保存，不显示成功消息
    hasUnsavedChanges.value = false
    lastSaved.value = new Date()
  } catch (error) {
    console.warn('自动保存失败:', error)
  } finally {
    autoSaving.value = false
  }
}

// 格式化最后保存时间
const formatLastSaved = (date) => {
  const now = new Date()
  const diff = now - date
  const minutes = Math.floor(diff / 60000)

  if (minutes < 1) {
    return '刚刚保存'
  } else if (minutes < 60) {
    return `${minutes}分钟前保存`
  } else {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }) + ' 保存'
  }
}

// 键盘快捷键
const handleKeydown = (event) => {
  // Ctrl+S 保存
  if ((event.ctrlKey || event.metaKey) && event.key === 's') {
    event.preventDefault()
    saveArticle(true, 'draft')
  }
  // Ctrl+Enter 发布
  else if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
    event.preventDefault()
    saveArticle(true, 'published')
  }
  // Esc 返回
  else if (event.key === 'Escape') {
    event.preventDefault()
    handleBack()
  }
}

// 标签输入
const tagInput = ref('')
const inputVisible = ref(false)

// 方法
const loadArticle = async (id) => {
  if (!id) return

  loading.value = true
  try {
    // 从store中查找文章基本信息
    const articlesStore = useArticlesStore()
    const foundArticle = articlesStore.articles.find(a => a.id == id)

    if (foundArticle) {
      // 优先使用store中已编辑的内容
      if (foundArticle.content && foundArticle.content !== foundArticle.fullSummary) {
        // 如果store中有编辑过的内容，直接使用
        article.value = {
          id: foundArticle.id,
          title: foundArticle.title,
          content: foundArticle.content,
          summary: foundArticle.summary,
          status: foundArticle.status || 'draft',
          category: foundArticle.category || '',
          tags: foundArticle.tags || [],
          createdAt: foundArticle.createdAt,
          updatedAt: foundArticle.updatedAt
        }
      } else {
        // 如果没有编辑过的内容，从数据库加载
        const fullArticleData = await articlesStore.loadArticle(foundArticle.id)

        if (fullArticleData) {
          article.value = {
            id: foundArticle.id,
            title: fullArticleData.title || foundArticle.title,
            content: fullArticleData.content || '开始编写您的文章内容...',
            summary: fullArticleData.news_points || fullArticleData.summary || foundArticle.summary,
            status: fullArticleData.status || foundArticle.status || 'draft',
            category: foundArticle.category || '',
            tags: foundArticle.tags || [],
            createdAt: foundArticle.createdAt,
            updatedAt: foundArticle.updatedAt
          }
        } else {
          // 如果API加载失败，使用store中的数据
          article.value = {
            ...foundArticle,
            content: foundArticle.content || '开始编写您的文章内容...'
          }
        }
      }
      isEditing.value = true
    } else {
      ElMessage.error('文章不存在')
      handleBack()
    }
  } catch (error) {
    console.error('加载文章失败:', error)
    ElMessage.error('加载文章失败')
    handleBack()
  } finally {
    loading.value = false
  }
}

const saveArticle = async (showMessage = true, status = 'draft') => {
  // 如果是自动保存且内容为空，跳过
  if (!showMessage && (!article.value.title.trim() && !article.value.content.trim())) {
    return
  }

  if (showMessage) {
    if (!article.value.title.trim()) {
      ElMessage.warning('请输入文章标题')
      return
    }

    if (!article.value.content.trim()) {
      ElMessage.warning('请输入文章内容')
      return
    }
  }

  saving.value = true
  try {
    const articlesStore = useArticlesStore()

    // 更新文章数据
    article.value.status = status
    article.value.updatedAt = new Date().toLocaleString('zh-CN')

    if (!isEditing.value) {
      // 新文章 - 保存到数据库
      console.log('创建新文章到数据库')
      const createResult = await createArticle({
        topic: article.value.title || '新文章',
        title: article.value.title,
        content: article.value.content,
        news_points: article.value.summary,
        status: article.value.status
      })

      if (createResult.success) {
        article.value.id = createResult.data.id
        article.value.createdAt = article.value.updatedAt
        isEditing.value = true
        console.log('新文章创建成功，ID:', article.value.id)

        // 更新store
        articlesStore.addArticle({
          ...article.value,
          fileName: `article_${article.value.id}.txt`
        })
      } else {
        throw new Error(createResult.error)
      }
    } else {
      // 更新现有文章 - 先更新store
      articlesStore.updateArticle(article.value.id, {
        title: article.value.title,
        content: article.value.content,
        summary: article.value.summary,
        status: article.value.status,
        category: article.value.category,
        tags: article.value.tags,
        updatedAt: article.value.updatedAt
      })

      // 然后保存到数据库
      console.log('准备保存到数据库，文章ID:', article.value.id)
      const saveResult = await saveArticleToDatabase(article.value.id, {
        title: article.value.title,
        content: article.value.content,
        news_points: article.value.summary,
        status: article.value.status
      })

      console.log('保存结果:', saveResult)
      if (!saveResult.success) {
        throw new Error(saveResult.error)
      } else {
        console.log('数据库保存成功!')
      }
    }

    // 只在手动保存时显示消息
    if (showMessage) {
      const statusText = status === 'published' ? '发布' : '保存'
      ElMessage.success(`文章${statusText}成功`)
    }

    hasUnsavedChanges.value = false
    lastSaved.value = new Date()

    if (status === 'published') {
      handleBack()
    }
  } catch (error) {
    console.error('保存文章失败:', error)
    ElMessage.error('保存失败，请重试')
  } finally {
    saving.value = false
  }
}

const handleBack = () => {
  // 检查是否有返回查询参数
  const returnQuery = route.query.returnQuery
  if (returnQuery) {
    try {
      const parsedQuery = JSON.parse(returnQuery)
      router.push({ path: '/articles', query: parsedQuery })
    } catch (error) {
      console.error('解析返回查询参数失败:', error)
      router.push('/articles')
    }
  } else {
    router.push('/articles')
  }
}

const generateSummary = () => {
  if (!article.value.content) {
    ElMessage.warning('请先输入文章内容')
    return
  }

  // 简单的摘要生成逻辑
  const textContent = article.value.content.replace(/<[^>]*>/g, '')
  const sentences = textContent.split(/[。！？]/).filter(s => s.trim().length > 10)
  article.value.summary = sentences.slice(0, 2).join('。') + '。'

  ElMessage.success('摘要生成成功')
}

const handleTagClose = (tag) => {
  article.value.tags.splice(article.value.tags.indexOf(tag), 1)
}

const showTagInput = () => {
  inputVisible.value = true
}

const handleTagInputConfirm = () => {
  if (tagInput.value && !article.value.tags.includes(tagInput.value)) {
    article.value.tags.push(tagInput.value)
  }
  inputVisible.value = false
  tagInput.value = ''
}

const previewArticle = () => {
  // 这里可以打开预览窗口或跳转到预览页面
  ElMessage.info('预览功能开发中...')
}

// 键盘快捷键已在上面定义

// 监听内容变化
watch([() => article.value.title, () => article.value.content, () => article.value.summary], () => {
  hasUnsavedChanges.value = true
}, { deep: true })

onMounted(() => {
  const articleId = route.params.id
  if (articleId && articleId !== 'new') {
    isEditing.value = true
    loadArticle(articleId)
  } else {
    // 新文章的默认内容
    article.value.content = '<p>开始编写您的文章内容...</p>'
  }

  // 启动自动保存
  startAutoSave()

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  stopAutoSave()
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<template>
  <div class="article-edit">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>
    
    <!-- 编辑器 -->
    <div v-else class="editor-container">
      <!-- 工具栏 -->
      <div class="editor-toolbar">
        <div class="toolbar-left">
          <el-button :icon="ArrowLeft" @click="handleBack">
            返回
          </el-button>
          <span class="editor-title">
            {{ isEditing ? '编辑文章' : '创建文章' }}
          </span>
        </div>
        
        <div class="toolbar-right">
          <!-- 状态信息 -->
          <div class="editor-status">
            <span class="word-count">{{ wordCount }} 字</span>
            <span v-if="autoSaving" class="auto-save-status saving">
              自动保存中...
            </span>
            <span v-else-if="lastSaved" class="auto-save-status saved">
              {{ formatLastSaved(lastSaved) }}
            </span>
            <span v-else-if="hasUnsavedChanges" class="auto-save-status unsaved">
              有未保存的更改
            </span>
          </div>

          <el-button @click="previewArticle" :icon="View">
            预览
          </el-button>
          <el-tooltip content="快捷键: Ctrl+S" placement="bottom">
            <el-button
              @click="saveArticle(true, 'draft')"
              :loading="saving"
              :icon="Document"
            >
              保存草稿
            </el-button>
          </el-tooltip>
          <el-tooltip content="快捷键: Ctrl+Enter" placement="bottom">
            <el-button
              type="primary"
              @click="saveArticle(true, 'published')"
              :loading="saving"
              :icon="Check"
            >
              发布文章
            </el-button>
          </el-tooltip>
        </div>
      </div>
      
      <!-- 编辑区域 -->
      <div class="editor-content">
        <!-- 左侧编辑器 -->
        <div class="editor-main">
          <!-- 标题输入 -->
          <div class="title-section">
            <el-input
              v-model="article.title"
              placeholder="请输入文章标题..."
              size="large"
              class="title-input"
            />
          </div>
          
          <!-- 富文本编辑器 -->
          <div class="content-section">
            <QuillEditor
              :content="article.content"
              :options="editorOptions"
              contentType="html"
              class="quill-editor"
              @update:content="(content) => article.content = content"
            />
          </div>
        </div>
        
        <!-- 右侧设置面板 -->
        <div class="editor-sidebar">
          <!-- 文章设置 -->
          <div class="setting-card">
            <h3 class="card-title">文章设置</h3>
            
            <div class="setting-item">
              <label class="setting-label">状态</label>
              <el-select v-model="article.status" style="width: 100%">
                <el-option label="草稿" value="draft" />
                <el-option label="已发布" value="published" />
                <el-option label="已归档" value="archived" />
              </el-select>
            </div>
            
            <div class="setting-item">
              <label class="setting-label">分类</label>
              <el-select v-model="article.category" placeholder="选择分类" style="width: 100%">
                <el-option
                  v-for="option in categoryOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </div>
            
            <div class="setting-item">
              <label class="setting-label">标签</label>
              <div class="tags-container">
                <el-tag
                  v-for="tag in article.tags"
                  :key="tag"
                  closable
                  @close="handleTagClose(tag)"
                  class="tag-item"
                >
                  {{ tag }}
                </el-tag>
                <el-input
                  v-if="inputVisible"
                  v-model="tagInput"
                  size="small"
                  @keyup.enter="handleTagInputConfirm"
                  @blur="handleTagInputConfirm"
                  class="tag-input"
                />
                <el-button
                  v-else
                  size="small"
                  @click="showTagInput"
                  class="add-tag-btn"
                >
                  + 添加标签
                </el-button>
              </div>
            </div>
          </div>
          
          <!-- 摘要设置 -->
          <div class="setting-card">
            <div class="card-header">
              <h3 class="card-title">文章摘要</h3>
              <el-button size="small" text @click="generateSummary">
                自动生成
              </el-button>
            </div>
            
            <el-input
              v-model="article.summary"
              type="textarea"
              :rows="4"
              placeholder="请输入文章摘要..."
              maxlength="200"
              show-word-limit
            />
          </div>
          
          <!-- 文章信息 -->
          <div v-if="isEditing" class="setting-card">
            <h3 class="card-title">文章信息</h3>
            
            <div class="info-item">
              <span class="info-label">创建时间：</span>
              <span class="info-value">{{ article.createdAt }}</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">更新时间：</span>
              <span class="info-value">{{ article.updatedAt }}</span>
            </div>
            
            <div class="info-item">
              <span class="info-label">字数统计：</span>
              <span class="info-value">
                {{ article.content.replace(/<[^>]*>/g, '').length }} 字
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.article-edit {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

.loading-container {
  padding: var(--spacing-xl);
}

.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-xl);
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-color);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.editor-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.toolbar-right {
  display: flex;
  gap: var(--spacing-sm);
}

.editor-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.editor-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.title-section {
  padding: var(--spacing-xl);
  background: var(--bg-card);
  border-bottom: 1px solid var(--border-color);
}

.title-input {
  font-size: 24px;
  font-weight: 600;
}

.title-input :deep(.el-input__wrapper) {
  background: transparent;
  border: none;
  box-shadow: none;
  padding: 0;
}

.title-input :deep(.el-input__inner) {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
}

.content-section {
  flex: 1;
  overflow: auto;
  max-height: calc(100vh - 200px);
}

.quill-editor {
  height: calc(100vh - 280px);
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  background: white;
}

.quill-editor :deep(.ql-container) {
  height: calc(100% - 42px);
  font-size: 16px;
  line-height: 1.6;
  overflow-y: auto;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.quill-editor :deep(.ql-editor) {
  padding: 20px;
  color: #303133;
  min-height: 400px;
}

.quill-editor :deep(.ql-toolbar) {
  border-bottom: 1px solid #dcdfe6;
  background: #f5f7fa;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.quill-editor :deep(.ql-editor.ql-blank::before) {
  color: #c0c4cc;
  font-style: normal;
}

.editor-sidebar {
  width: 320px;
  background: var(--bg-secondary);
  border-left: 1px solid var(--border-color);
  overflow-y: auto;
  padding: var(--spacing-xl);
}

.setting-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.setting-item {
  margin-bottom: var(--spacing-md);
}

.setting-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  align-items: center;
}

.tag-item {
  margin: 0;
}

.tag-input {
  width: 80px;
}

.add-tag-btn {
  border-style: dashed;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
  font-size: 14px;
}

.info-label {
  color: var(--text-secondary);
}

.info-value {
  color: var(--text-primary);
}

@media (max-width: 768px) {
  .editor-content {
    flex-direction: column;
  }
  
  .editor-sidebar {
    width: 100%;
    max-height: 300px;
  }
  
  .toolbar-right {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}

/* 编辑器状态样式 */
.editor-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-right: var(--spacing-md);
  font-size: 12px;
  line-height: 1.4;
}

.word-count {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 14px;
}

.auto-save-status {
  color: var(--text-secondary);
  font-size: 11px;
}

.auto-save-status.saving {
  color: #409eff;
}

.auto-save-status.saved {
  color: #67c23a;
}

.auto-save-status.unsaved {
  color: #e6a23c;
}

/* 优化编辑器字体大小 */
.quill-editor :deep(.ql-editor) {
  font-size: 20px;
  line-height: 1.8;
  min-height: 400px;
}

.quill-editor :deep(.ql-editor h1) {
  font-size: 28px;
}

.quill-editor :deep(.ql-editor h2) {
  font-size: 24px;
}

.title-input :deep(.el-input__inner) {
  font-size: 24px;
  font-weight: 600;
}
</style>
