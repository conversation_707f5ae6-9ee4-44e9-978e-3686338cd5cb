#!/bin/bash

# 服务器部署脚本
# 服务器IP: http://**************/
# 数据库密码: Huangkun729.
# 端口: 9485

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "=================================="
echo "🚀 文章管理系统 - 服务器部署"
echo "🌐 服务器: http://**************/"
echo "🔑 数据库: Huangkun729."
echo "📡 端口: 9485"
echo "=================================="
echo

# 检查Docker
if ! command -v docker &> /dev/null; then
    log_error "Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! docker info >/dev/null 2>&1; then
    log_error "Docker 服务未运行，请启动 Docker 服务"
    exit 1
fi

# 创建生产环境配置
log_info "创建生产环境配置..."
cat > .env << EOF
# 服务器部署配置
# 服务器IP: http://**************/
# 数据库密码: Huangkun729.

# 数据库配置 - 使用host网络模式连接localhost
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=Huangkun729.
DB_NAME=news_system

# 应用配置
NODE_ENV=production
PORT=9485
HOST=0.0.0.0

# 时区设置
TZ=Asia/Shanghai

# API配置
VITE_API_BASE_URL=http://**************:9485/api
EOF

log_success "生产环境配置已创建"

# 创建数据目录
if [ ! -d "data" ]; then
    log_info "创建数据目录..."
    mkdir -p data
    log_success "数据目录已创建"
fi

# 停止现有服务
if docker compose ps | grep -q "smart-notes"; then
    log_info "停止现有服务..."
    docker compose down
fi

# 构建和启动服务
log_info "构建 Docker 镜像..."
docker compose build --no-cache

log_info "启动服务..."
docker compose up -d

# 等待服务启动
log_info "等待服务启动..."
sleep 15

# 检查服务状态
if docker compose ps | grep -q "Up"; then
    log_success "服务启动成功！"
    
    echo
    echo "=================================="
    echo "🎉 部署完成！"
    echo "=================================="
    echo "📍 访问地址: http://**************:9485"
    echo "🔧 API地址:  http://**************:9485/api"
    echo "📊 服务状态: docker compose ps"
    echo "📋 查看日志: docker compose logs -f smart-notes"
    echo "🛑 停止服务: docker compose down"
    echo "=================================="
    echo
    
    # 显示服务状态
    echo "当前服务状态："
    docker compose ps
    
    # 测试API连接
    log_info "测试API连接..."
    if curl -f http://localhost:9485/api/stats >/dev/null 2>&1; then
        log_success "API连接正常"
    else
        log_warning "API连接测试失败，请检查服务状态"
    fi
    
else
    log_error "服务启动失败"
    echo
    echo "故障排除："
    echo "1. 查看详细日志: docker compose logs smart-notes"
    echo "2. 检查端口占用: lsof -i :9485"
    echo "3. 检查MySQL服务: sudo systemctl status mysql"
    echo "4. 检查数据库连接: mysql -h localhost -u root -p"
    exit 1
fi

echo
log_info "部署完成！请确保："
echo "1. MySQL服务正在运行"
echo "2. 防火墙允许9485端口访问"
echo "3. 数据库用户权限正确配置"
echo
echo "如需外网访问，请配置防火墙规则："
echo "sudo ufw allow 9485"
echo "或"
echo "sudo firewall-cmd --permanent --add-port=9485/tcp"
echo "sudo firewall-cmd --reload"
