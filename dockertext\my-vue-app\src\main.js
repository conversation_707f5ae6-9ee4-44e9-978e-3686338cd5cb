import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css'

import App from './App.vue'
import './assets/styles/main.scss'

// 路由配置
const routes = [
  { path: '/', redirect: '/dashboard' },
  { path: '/dashboard', component: () => import('./views/Dashboard/Dashboard.vue') },
  { path: '/articles', component: () => import('./views/ArticleList/ArticleList.vue') },
  { path: '/article/edit/:id?', component: () => import('./views/ArticleEdit/ArticleEdit.vue') },
  { path: '/import', component: () => import('./views/Import/Import.vue') }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

const pinia = createPinia()
const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(ElementPlus)
app.use(router)
app.use(pinia)
app.component('QuillEditor', QuillEditor)

app.mount('#app')
