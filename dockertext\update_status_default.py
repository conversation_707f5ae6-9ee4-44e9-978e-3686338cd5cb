#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修改articles表的status字段默认值为draft
"""

import mysql.connector
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': 'mysql-24e746d3-kbxy2365806687-c9eb.b.aivencloud.com',
    'port': 13304,
    'user': 'avnadmin',
    'password': 'AVNS_kX8_YlNEfE3RquyPaG6',
    'database': 'defaultdb',
    'charset': 'utf8mb4',
    'ssl_disabled': False
}

def update_status_default():
    """修改status字段的默认值为draft"""
    
    try:
        # 连接数据库
        print("🔗 正在连接数据库...")
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        print("✅ 数据库连接成功")
        
        # 查看当前表结构
        print("\n📋 查看当前articles表结构:")
        cursor.execute("DESCRIBE articles")
        columns = cursor.fetchall()
        
        for column in columns:
            if column[0] == 'status':
                print(f"   当前status字段: {column}")
        
        # 修改status字段默认值
        print("\n🔧 正在修改status字段默认值...")
        alter_query = """
        ALTER TABLE articles 
        MODIFY COLUMN status ENUM('draft', 'published', 'archived') 
        DEFAULT 'draft' 
        COMMENT '文章状态'
        """
        
        cursor.execute(alter_query)
        connection.commit()
        print("✅ status字段默认值已修改为 'draft'")
        
        # 验证修改结果
        print("\n🔍 验证修改结果:")
        cursor.execute("DESCRIBE articles")
        columns = cursor.fetchall()
        
        for column in columns:
            if column[0] == 'status':
                print(f"   修改后status字段: {column}")
        
        # 可选：查看当前文章的状态分布
        print("\n📊 当前文章状态分布:")
        cursor.execute("""
        SELECT status, COUNT(*) as count 
        FROM articles 
        GROUP BY status 
        ORDER BY count DESC
        """)
        
        status_stats = cursor.fetchall()
        for stat in status_stats:
            print(f"   {stat[0]}: {stat[1]} 篇文章")
        
        # 询问是否要将现有的published文章改为draft
        print(f"\n❓ 发现 {sum(stat[1] for stat in status_stats if stat[0] == 'published')} 篇已发布文章")
        
        # 这里我们不自动修改，让用户决定
        print("💡 如果需要将现有已发布文章改为草稿，请手动执行:")
        print("   UPDATE articles SET status = 'draft' WHERE status = 'published';")
        
        print(f"\n🎉 修改完成！现在新创建的文章默认状态为 'draft'")
        
    except mysql.connector.Error as error:
        print(f"❌ 数据库操作失败: {error}")
        
    except Exception as error:
        print(f"❌ 执行失败: {error}")
        
    finally:
        # 关闭连接
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()
        print("🔒 数据库连接已关闭")

def test_new_article_creation():
    """测试新文章创建时的默认状态"""
    
    try:
        print("\n🧪 测试新文章创建...")
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 创建测试文章
        test_query = """
        INSERT INTO articles (topic, title, content, news_points, word_count) 
        VALUES (%s, %s, %s, %s, %s)
        """
        
        test_values = (
            '测试话题',
            '测试文章标题',
            '这是一个测试文章内容，用于验证默认状态。',
            '测试要点',
            30
        )
        
        cursor.execute(test_query, test_values)
        connection.commit()
        
        # 获取刚创建的文章
        article_id = cursor.lastrowid
        cursor.execute("SELECT id, title, status FROM articles WHERE id = %s", (article_id,))
        article = cursor.fetchone()
        
        print(f"✅ 测试文章创建成功:")
        print(f"   ID: {article[0]}")
        print(f"   标题: {article[1]}")
        print(f"   状态: {article[2]} {'✅' if article[2] == 'draft' else '❌'}")
        
        # 删除测试文章
        cursor.execute("DELETE FROM articles WHERE id = %s", (article_id,))
        connection.commit()
        print(f"🗑️ 测试文章已删除")
        
    except Exception as error:
        print(f"❌ 测试失败: {error}")
        
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    print("🚀 开始修改articles表的status字段默认值")
    print("=" * 50)
    
    # 执行修改
    update_status_default()
    
    # 测试新文章创建
    test_new_article_creation()
    
    print("=" * 50)
    print("✨ 所有操作完成！")
