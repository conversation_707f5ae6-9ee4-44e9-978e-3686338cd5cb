# 🚀 智能笔记网站 - 完整部署教程

## 📋 **部署前准备**

### 1. 服务器要求
- **操作系统**: Linux (Ubuntu 20.04+ 推荐)
- **内存**: 至少 2GB
- **磁盘**: 至少 5GB 可用空间
- **Docker**: 20.0+
- **Docker Compose**: 2.0+

### 2. 检查服务器环境
```bash
# 检查Docker版本
docker --version
docker-compose --version

# 如果没有安装Docker，执行以下命令：
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

## 📁 **第一步：上传项目文件**

### 方法1：Git克隆（推荐）
```bash
# 克隆项目
git clone https://github.com/KyrieOkun/-231.git
cd -231

# 或者如果仓库名不同，替换为实际的仓库地址
```

### 方法2：手动上传
```bash
# 创建项目目录
mkdir smart-notes-website
cd smart-notes-website

# 将以下文件上传到服务器：
# - Dockerfile
# - docker-compose.yml
# - .env.example
# - my-vue-app/ (整个文件夹)
# - 生成文章_20250629_174526/ (整个文件夹)
```

## ⚙️ **第二步：配置环境变量（重要！有两个.env文件）**

### 1. 配置后端环境变量（根目录）
```bash
# 复制后端环境变量模板
cp .env.example .env

# 编辑后端配置文件
nano .env
```

**后端 .env 文件内容：**
```env
# 服务器配置
PORT=9486
HOST=0.0.0.0
NODE_ENV=production

# 文章文件夹路径（确保路径正确）
ARTICLES_FOLDER=./生成文章_20250629_174526

# 数据库配置（使用您的实际数据库信息）
DB_HOST=mysql-24e746d3-kbxy2365806687-c9eb.b.aivencloud.com
DB_PORT=13304
DB_USER=avnadmin
DB_PASSWORD=AVNS_kX8_YlNEfE3RquyPaG6
DB_NAME=defaultdb

# CORS配置（改为您的服务器IP或域名）
CORS_ORIGIN=http://your-server-ip:9486

# 其他配置
LOG_LEVEL=warn
```

### 2. 配置前端环境变量（my-vue-app目录）
```bash
# 复制前端环境变量模板
cp my-vue-app/.env.example my-vue-app/.env

# 编辑前端配置文件
nano my-vue-app/.env
```

**前端 my-vue-app/.env 文件内容：**
```env
# API配置（重要！改为您的服务器IP）
VITE_API_BASE_URL=http://your-server-ip:9486/api

# 应用配置
VITE_APP_TITLE=智能笔记网站
VITE_APP_VERSION=1.0.0

# 其他配置
VITE_ENABLE_MOCK=false
```

### 3. 重要配置说明
- **后端 CORS_ORIGIN**: 改为您的服务器IP地址，如 `http://123.456.789.123:9486`
- **前端 VITE_API_BASE_URL**: 改为您的服务器IP地址，如 `http://123.456.789.123:9486/api`
- **ARTICLES_FOLDER**: 确保文章文件夹存在且路径正确
- **数据库配置**: 如果您有自己的数据库，请修改相应配置

## 🔥 **第三步：开放防火墙端口**

```bash
# Ubuntu/Debian 系统
sudo ufw allow 9486
sudo ufw reload

# CentOS/RHEL 系统
sudo firewall-cmd --add-port=9486/tcp --permanent
sudo firewall-cmd --reload

# 检查端口是否开放
sudo netstat -tlnp | grep 9486
```

## 🐳 **第四步：Docker部署**

### 1. 构建并启动服务
```bash
# 确保在项目根目录
pwd
ls -la  # 应该能看到 Dockerfile 和 docker-compose.yml

# 构建并启动（第一次部署）
docker-compose up -d --build

# 查看启动状态
docker-compose ps
```

### 2. 查看日志（重要！）
```bash
# 查看实时日志
docker-compose logs -f smart-notes

# 如果有错误，查看详细日志
docker-compose logs smart-notes
```

### 3. 验证部署成功
```bash
# 检查容器状态
docker-compose ps

# 测试健康检查
curl http://localhost:9486/health

# 测试API
curl http://localhost:9486/api/articles
```

## 🌐 **第五步：访问应用**

### 1. 浏览器访问
- **应用地址**: `http://your-server-ip:9486`
- **API地址**: `http://your-server-ip:9486/api`
- **健康检查**: `http://your-server-ip:9486/health`

### 2. 如果无法访问，检查：
```bash
# 检查容器是否运行
docker-compose ps

# 检查端口是否监听
sudo netstat -tlnp | grep 9486

# 检查防火墙
sudo ufw status

# 查看详细日志
docker-compose logs smart-notes
```

## 🔧 **常用管理命令**

### 启动/停止服务
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 重新构建并启动
docker-compose up -d --build
```

### 查看状态和日志
```bash
# 查看容器状态
docker-compose ps

# 查看实时日志
docker-compose logs -f

# 查看资源使用
docker stats
```

### 数据备份
```bash
# 备份数据目录
tar -czf backup-$(date +%Y%m%d).tar.gz ./data ./生成文章_20250629_174526

# 恢复数据
tar -xzf backup-20250701.tar.gz
```

## 🚨 **故障排除**

### 1. 容器启动失败
```bash
# 查看详细错误
docker-compose logs smart-notes

# 检查配置文件
cat .env
cat docker-compose.yml

# 重新构建
docker-compose down
docker-compose up -d --build
```

### 2. 端口被占用
```bash
# 查看端口占用
sudo lsof -i :9486

# 杀死占用进程
sudo kill -9 <PID>
```

### 3. 文件权限问题
```bash
# 修复文件权限
sudo chown -R $USER:$USER ./
chmod -R 755 ./
```

### 4. 数据库连接失败
```bash
# 检查数据库配置
cat .env | grep DB_

# 测试数据库连接
mysql -h mysql-24e746d3-kbxy2365806687-c9eb.b.aivencloud.com -P 13304 -u avnadmin -p defaultdb
```

## ✅ **部署成功标志**

当您看到以下内容时，说明部署成功：

1. **容器状态正常**:
   ```bash
   docker-compose ps
   # 显示 smart-notes 容器状态为 Up
   ```

2. **健康检查通过**:
   ```bash
   curl http://localhost:9486/health
   # 返回: {"status":"ok","timestamp":"..."}
   ```

3. **浏览器能正常访问**: `http://your-server-ip:9486`

## 🚀 **快速部署命令总结**

```bash
# 1. 配置后端环境变量
cp .env.example .env
nano .env  # 修改CORS_ORIGIN为您的服务器IP

# 2. 配置前端环境变量
cp my-vue-app/.env.example my-vue-app/.env
nano my-vue-app/.env  # 修改VITE_API_BASE_URL为您的服务器IP

# 3. 开放端口
sudo ufw allow 9486

# 4. 启动服务
docker-compose up -d --build

# 5. 验证部署
curl http://localhost:9486/health
```

## 🎯 **完成！**

恭喜！您的智能笔记网站已经成功部署！

- 🌐 **访问地址**: `http://your-server-ip:9486`
- 📊 **管理后台**: 直接在网站中管理文章
- 🔧 **维护**: 使用上述管理命令进行日常维护

如有问题，请查看日志：`docker-compose logs -f smart-notes`
