import{_ as fe,c as B,v as h,x as E,o as ge,a as v,b as r,e as o,d as T,f as a,w as s,g as u,u as y,h as ye,y as I,r as n,F as A,m as D,z as L,t as k,q as R,A as be,B as he,E as Y,C as ke}from"./index-Bkyzfu8E.js";import{u as Ce}from"./articles-CdUSxqXs.js";const we={class:"article-list"},Se={class:"page-header"},Ve={class:"header-actions"},xe={class:"filters-section"},ze={class:"search-bar"},Be={class:"filters"},Ae={class:"articles-container"},De={key:0,class:"loading-container"},Oe={key:1,class:"empty-container"},$e={key:2,class:"articles-grid"},Me={class:"card-header"},Ne={class:"article-status"},Te={class:"article-category"},Ye=["onClick"],Fe={class:"article-title"},Qe={class:"article-summary"},Ue={class:"card-footer"},qe={class:"article-meta"},Ee={class:"meta-item"},Ie={class:"meta-item"},Le={class:"meta-item"},Re={key:0,class:"pagination-container"},Pe={__name:"ArticleList",setup(je){const C=ye(),d=Ce(),O=B(()=>d.articles),P=B(()=>d.loading),F=B(()=>d.pagination.total),V=B({get:()=>d.searchQuery,set:t=>{d.setSearchQuery(t),c.value=1}}),b=h(new Date().toISOString().split("T")[0]),p=h(""),g=h(""),_=h("createdAt"),i=h("desc"),j=[{label:"全部状态",value:""},{label:"草稿",value:"draft"},{label:"已发布",value:"published"},{label:"已归档",value:"archived"}],Q=[{label:"全部分类",value:""},{label:"名人轶事",value:"celebrity"},{label:"国际局势",value:"international"},{label:"科技动态",value:"technology"},{label:"财经观察",value:"finance"},{label:"体育赛事",value:"sports"}],J=[{label:"创建时间",value:"createdAt"},{label:"更新时间",value:"updatedAt"},{label:"标题",value:"title"},{label:"浏览量",value:"views"}],c=h(1),w=h(20),S=async()=>{const t={page:c.value,limit:w.value,date:b.value,status:p.value,topic:V.value,sortBy:_.value,sortOrder:i.value};await d.loadArticles(t)},U=t=>{const e=C.currentRoute.value.query;C.push({path:`/article/edit/${t.id}`,query:{returnQuery:JSON.stringify(e)}})},G=async t=>{try{await ke.confirm(`确定要删除文章"${t.title}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),d.deleteArticle(t.id),Y.success("删除成功")}catch{}},q=async(t,e)=>{try{d.updateArticle(t.id,{status:e}),Y.success("状态更新成功")}catch{Y.error("状态更新失败")}},H=t=>({draft:"warning",published:"success",archived:"info"})[t]||"default",K=t=>({draft:"草稿",published:"已发布",archived:"已归档"})[t]||t,W=t=>{const e=Q.find(m=>m.value===t);return e?e.label:t},X=t=>{if(!t)return"未知时间";try{const e=new Date(t);return isNaN(e.getTime())?"无效日期":e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch{return"日期错误"}},Z=t=>{c.value=t,$(),S()},ee=t=>{w.value=t,c.value=1,$(),S()},te=()=>{d.resetFilters(),b.value=new Date().toISOString().split("T")[0],p.value="",g.value="",_.value="createdAt",i.value="desc",c.value=1,S()};E([b,p,g,_,i],()=>{c.value=1,$(),S(),d.setFilters({status:p.value,category:g.value,sortBy:_.value,sortOrder:i.value})}),E(V,()=>{c.value=1,S()});const le=()=>{const t=C.currentRoute.value.query;t.date&&(b.value=t.date),t.status&&(p.value=t.status),t.category&&(g.value=t.category),t.sortBy&&(_.value=t.sortBy),t.sortOrder&&(i.value=t.sortOrder),t.page&&(c.value=parseInt(t.page)),t.pageSize&&(w.value=parseInt(t.pageSize))},$=()=>{const t={date:b.value,page:c.value,pageSize:w.value};p.value&&(t.status=p.value),g.value&&(t.category=g.value),_.value!=="createdAt"&&(t.sortBy=_.value),i.value!=="desc"&&(t.sortOrder=i.value),C.replace({query:t})};return ge(()=>{le(),S()}),(t,e)=>{const m=n("el-button"),ae=n("el-input"),se=n("el-date-picker"),M=n("el-option"),N=n("el-select"),f=n("el-icon"),oe=n("el-button-group"),ne=n("el-skeleton"),ue=n("el-empty"),re=n("el-tag"),x=n("el-dropdown-item"),ie=n("Check"),de=n("Box"),ce=n("el-dropdown-menu"),ve=n("el-dropdown"),pe=n("Clock"),_e=n("Document"),me=n("el-pagination");return r(),v("div",we,[o("div",Se,[e[11]||(e[11]=o("div",{class:"header-content"},[o("h1",{class:"page-title"},"文章管理"),o("p",{class:"page-subtitle"},"管理您的所有文章内容")],-1)),o("div",Ve,[a(m,{type:"primary",icon:y(I),onClick:e[0]||(e[0]=l=>y(C).push("/article/edit"))},{default:s(()=>e[9]||(e[9]=[u(" 创建文章 ")])),_:1,__:[9]},8,["icon"]),a(m,{icon:t.Upload,onClick:e[1]||(e[1]=l=>y(C).push("/import"))},{default:s(()=>e[10]||(e[10]=[u(" 导入文件 ")])),_:1,__:[10]},8,["icon"])])]),o("div",xe,[o("div",ze,[a(ae,{modelValue:V.value,"onUpdate:modelValue":e[2]||(e[2]=l=>V.value=l),placeholder:"搜索文章标题或内容...","prefix-icon":t.Search,size:"large",clearable:""},null,8,["modelValue","prefix-icon"])]),o("div",Be,[a(se,{modelValue:b.value,"onUpdate:modelValue":e[3]||(e[3]=l=>b.value=l),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",size:"default",style:{width:"160px"}},null,8,["modelValue"]),a(N,{modelValue:p.value,"onUpdate:modelValue":e[4]||(e[4]=l=>p.value=l),placeholder:"状态筛选",clearable:""},{default:s(()=>[(r(),v(A,null,D(j,l=>a(M,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),a(N,{modelValue:g.value,"onUpdate:modelValue":e[5]||(e[5]=l=>g.value=l),placeholder:"分类筛选",clearable:""},{default:s(()=>[(r(),v(A,null,D(Q,l=>a(M,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),a(N,{modelValue:_.value,"onUpdate:modelValue":e[6]||(e[6]=l=>_.value=l),placeholder:"排序方式"},{default:s(()=>[(r(),v(A,null,D(J,l=>a(M,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),a(oe,null,{default:s(()=>[a(m,{type:i.value==="asc"?"primary":"default",onClick:e[7]||(e[7]=l=>i.value="asc")},{default:s(()=>[a(f,null,{default:s(()=>[a(y(L))]),_:1}),e[12]||(e[12]=u(" 升序 "))]),_:1,__:[12]},8,["type"]),a(m,{type:i.value==="desc"?"primary":"default",onClick:e[8]||(e[8]=l=>i.value="desc")},{default:s(()=>[a(f,null,{default:s(()=>[a(y(L))]),_:1}),e[13]||(e[13]=u(" 降序 "))]),_:1,__:[13]},8,["type"])]),_:1}),a(m,{onClick:te},{default:s(()=>e[14]||(e[14]=[u("重置")])),_:1,__:[14]})])]),o("div",Ae,[P.value?(r(),v("div",De,[a(ne,{rows:5,animated:""})])):!O.value||O.value.length===0?(r(),v("div",Oe,[a(ue,{description:"暂无文章数据"})])):(r(),v("div",$e,[(r(!0),v(A,null,D(O.value,l=>(r(),v("div",{key:l.id,class:"article-card"},[o("div",Me,[o("div",Ne,[a(re,{type:H(l.status),size:"small"},{default:s(()=>[u(k(K(l.status)),1)]),_:2},1032,["type"]),o("span",Te,k(W(l.category)),1)]),a(ve,{trigger:"click"},{dropdown:s(()=>[a(ce,null,{default:s(()=>[a(x,{onClick:z=>U(l)},{default:s(()=>[a(f,null,{default:s(()=>[a(y(I))]),_:1}),e[15]||(e[15]=u(" 编辑 "))]),_:2,__:[15]},1032,["onClick"]),l.status==="draft"?(r(),R(x,{key:0,onClick:z=>q(l,"published")},{default:s(()=>[a(f,null,{default:s(()=>[a(ie)]),_:1}),e[16]||(e[16]=u(" 发布 "))]),_:2,__:[16]},1032,["onClick"])):T("",!0),l.status==="published"?(r(),R(x,{key:1,onClick:z=>q(l,"archived")},{default:s(()=>[a(f,null,{default:s(()=>[a(de)]),_:1}),e[17]||(e[17]=u(" 归档 "))]),_:2,__:[17]},1032,["onClick"])):T("",!0),a(x,{divided:"",onClick:z=>G(l)},{default:s(()=>[a(f,null,{default:s(()=>[a(y(be))]),_:1}),e[18]||(e[18]=u(" 删除 "))]),_:2,__:[18]},1032,["onClick"])]),_:2},1024)]),default:s(()=>[a(m,{text:"",icon:t.MoreFilled},null,8,["icon"])]),_:2},1024)]),o("div",{class:"card-content",onClick:z=>U(l)},[o("h3",Fe,k(l.title),1),o("p",Qe,k(l.summary),1)],8,Ye),o("div",Ue,[o("div",qe,[o("span",Ee,[a(f,null,{default:s(()=>[a(pe)]),_:1}),u(" "+k(X(l.created_at||l.createdAt)),1)]),o("span",Ie,[a(f,null,{default:s(()=>[a(y(he))]),_:1}),u(" "+k(l.views)+" 次浏览 ",1)]),o("span",Le,[a(f,null,{default:s(()=>[a(_e)]),_:1}),u(" "+k(l.wordCount)+" 字 ",1)])])])]))),128))]))]),F.value>w.value?(r(),v("div",Re,[a(me,{"current-page":c.value,"page-size":w.value,total:F.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",background:"",onCurrentChange:Z,onSizeChange:ee},null,8,["current-page","page-size","total"])])):T("",!0)])}}},He=fe(Pe,[["__scopeId","data-v-d797f1c4"]]);export{He as default};
