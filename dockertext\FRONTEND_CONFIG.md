# 前端API配置指南

## 📋 **配置概述**

前端API配置会根据部署环境自动选择合适的API地址：

```javascript
// 智能API地址选择逻辑
const getApiBaseUrl = () => {
  // 开发环境
  if (import.meta.env.DEV) {
    return import.meta.env.VITE_API_BASE_URL || 'http://localhost:9485/api'
  }

  // 生产环境 - 使用环境变量
  if (import.meta.env.VITE_API_BASE_URL) {
    return import.meta.env.VITE_API_BASE_URL
  }

  // 默认使用相对路径（同域部署）
  return '/api'
}
```

## 🎯 **不同部署场景配置**

### 场景1：本地开发/测试
**无需修改** - 自动使用 `http://localhost:9485/api`

```bash
# 开发环境自动配置
npm run dev  # 前端开发服务器
node database-server.js  # 后端API服务器
```

### 场景2：本地Docker部署
**无需修改** - 使用相对路径 `/api`

```bash
# Docker构建时会自动使用相对路径
docker compose up -d
# 访问: http://localhost:9485
```

### 场景3：服务器Docker部署（同域）
**推荐配置** - 使用相对路径

```bash
# .env 文件配置
VITE_API_BASE_URL=/api

# 访问: http://your-server:9485
# API: http://your-server:9485/api
```

### 场景4：服务器Docker部署（跨域）
**需要配置** - 指定完整API地址

```bash
# .env 文件配置
VITE_API_BASE_URL=http://*************:9485/api
# 或
VITE_API_BASE_URL=http://your-domain.com:9485/api
```

### 场景5：前后端分离部署
**需要配置** - 指定后端服务器地址

```bash
# 前端部署在 nginx (端口80)
# 后端部署在 Docker (端口9485)
VITE_API_BASE_URL=http://api.your-domain.com:9485/api
```

## 🔧 **配置步骤**

### 步骤1：确定部署方式
选择上述场景之一。

### 步骤2：配置环境变量
```bash
# 编辑 .env 文件
nano dockertext/my-vue-app/.env

# 或编辑 .env.production
nano dockertext/.env.production
```

### 步骤3：重新构建（如果需要）
```bash
# 如果修改了配置，需要重新构建
cd dockertext
docker compose down
docker compose build --no-cache
docker compose up -d
```

## 📝 **配置文件位置**

### 前端项目配置
```
dockertext/my-vue-app/
├── .env                    # 开发环境配置
├── .env.production        # 生产环境配置
└── src/api/articles.js    # API配置逻辑
```

### Docker项目配置
```
dockertext/
├── .env                   # Docker环境配置
└── .env.production       # 生产环境模板
```

## 🌐 **网络访问说明**

### 内网访问
```bash
# 服务器IP访问
http://*************:9485

# API地址
http://*************:9485/api
```

### 公网访问
```bash
# 域名访问
http://your-domain.com:9485

# API地址  
http://your-domain.com:9485/api
```

### 反向代理（推荐生产环境）
```nginx
# nginx 配置示例
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:9485;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api/ {
        proxy_pass http://localhost:9485/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔍 **验证配置**

### 检查API地址
```bash
# 查看构建后的配置
docker compose logs smart-notes | grep -i api

# 测试API连接
curl http://localhost:9485/api/stats
```

### 浏览器开发者工具
1. 打开浏览器开发者工具 (F12)
2. 查看 Network 标签
3. 确认API请求地址正确

### 常见API端点测试
```bash
# 统计数据
curl http://localhost:9485/api/stats

# 文章列表
curl http://localhost:9485/api/articles

# 健康检查
curl http://localhost:9485/api/health
```

## ⚠️ **常见问题**

### 问题1：CORS跨域错误
**原因**：前后端不在同一域名/端口
**解决**：
1. 使用相对路径配置
2. 或在后端添加CORS支持

### 问题2：API请求404
**原因**：API地址配置错误
**解决**：
1. 检查 `VITE_API_BASE_URL` 配置
2. 确认后端服务正常运行

### 问题3：开发环境API地址错误
**原因**：开发环境默认配置不匹配
**解决**：
```bash
# 创建开发环境配置
echo "VITE_API_BASE_URL=http://localhost:9485/api" > dockertext/my-vue-app/.env
```

## 📊 **配置对照表**

| 部署方式 | 前端地址 | API配置 | 说明 |
|----------|----------|---------|------|
| 本地开发 | http://localhost:5173 | `http://localhost:9485/api` | 自动配置 |
| 本地Docker | http://localhost:9485 | `/api` | 相对路径 |
| 服务器同域 | http://server:9485 | `/api` | 相对路径 |
| 服务器跨域 | http://frontend.com | `http://api.com:9485/api` | 完整地址 |
| 反向代理 | http://domain.com | `/api` | nginx代理 |

## 🚀 **推荐配置**

### 开发环境
```bash
# dockertext/my-vue-app/.env
VITE_API_BASE_URL=http://localhost:9485/api
```

### 生产环境（推荐）
```bash
# dockertext/.env.production
VITE_API_BASE_URL=/api  # 使用相对路径
```

### 跨域部署
```bash
# dockertext/.env.production
VITE_API_BASE_URL=http://your-server-ip:9485/api
```

---

**总结：大多数情况下，改为9485端口后前端无需额外配置，会自动适配！**
