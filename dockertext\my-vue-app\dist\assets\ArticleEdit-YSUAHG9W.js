import{_ as ee,v as d,c as te,x as le,o as ae,K as se,L as oe,a as c,b as i,f as u,r as m,e as s,w as r,g as f,u as k,M as ne,t as p,d as B,I as ue,i as ie,k as re,Q as de,F as $,m as q,q as T,N as ce,E as v,h as ve}from"./index-BpJfbBiC.js";import{u as O,c as me,s as fe}from"./articles-B88tJUEo.js";const pe={class:"article-edit"},_e={key:0,class:"loading-container"},ge={key:1,class:"editor-container"},ye={class:"editor-toolbar"},be={class:"toolbar-left"},we={class:"editor-title"},he={class:"toolbar-right"},Ae={class:"editor-status"},ke={class:"word-count"},Ce={key:0,class:"auto-save-status saving"},Ve={key:1,class:"auto-save-status saved"},Se={key:2,class:"auto-save-status unsaved"},xe={class:"editor-content"},Ee={class:"editor-main"},De={class:"title-section"},Ie={class:"content-section"},Te={class:"editor-sidebar"},Ne={class:"setting-card"},Ue={class:"setting-item"},Ke={class:"setting-item"},Le={class:"setting-item"},ze={class:"tags-container"},Be={class:"setting-card"},$e={class:"card-header"},qe={key:0,class:"setting-card"},Oe={class:"info-item"},Qe={class:"info-value"},Re={class:"info-item"},Fe={class:"info-value"},Me={class:"info-item"},je={class:"info-value"},Je=3e4,Ge={__name:"ArticleEdit",setup(He){const N=se(),S=ve(),e=d({id:null,title:"",content:"",summary:"",status:"draft",category:"",tags:[],createdAt:null,updatedAt:null}),x=d(!1),b=d(!1),_=d(!1),E=d(!1),C=d(null),w=d(!1),Q=te(()=>e.value.content?e.value.content.replace(/<[^>]*>/g,"").trim().length:0);let g=null;const R={theme:"snow",placeholder:"开始写作您的文章...",modules:{toolbar:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{indent:"-1"},{indent:"+1"}],[{size:["small",!1,"large","huge"]}],[{color:[]},{background:[]}],[{align:[]}],["clean"],["link","image"]]},bounds:document.body,scrollingContainer:null,strict:!1},F=[{label:"时政",value:"时政"},{label:"娱乐",value:"娱乐"},{label:"科技",value:"科技"},{label:"体育",value:"体育"},{label:"社会",value:"社会"},{label:"国际",value:"国际"},{label:"财经",value:"财经"},{label:"其他",value:"其他"}],M=()=>{g&&clearInterval(g),g=setInterval(async()=>{w.value&&!b.value&&await J()},Je)},j=()=>{g&&(clearInterval(g),g=null)},J=async()=>{if(!(!e.value.title.trim()&&!e.value.content.trim()))try{E.value=!0,await h(!1),w.value=!1,C.value=new Date}catch(a){console.warn("自动保存失败:",a)}finally{E.value=!1}},G=a=>{const l=new Date-a,o=Math.floor(l/6e4);return o<1?"刚刚保存":o<60?`${o}分钟前保存`:a.toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"})+" 保存"},U=a=>{(a.ctrlKey||a.metaKey)&&a.key==="s"?(a.preventDefault(),h(!0,"draft")):(a.ctrlKey||a.metaKey)&&a.key==="Enter"?(a.preventDefault(),h(!0,"published")):a.key==="Escape"&&(a.preventDefault(),A())},y=d(""),D=d(!1),H=async a=>{if(a){x.value=!0;try{const t=O(),l=t.articles.find(o=>o.id==a);if(l){if(l.content&&l.content!==l.fullSummary)e.value={id:l.id,title:l.title,content:l.content,summary:l.summary,status:l.status||"draft",category:l.category||"",tags:l.tags||[],createdAt:l.createdAt,updatedAt:l.updatedAt};else{const o=await t.loadArticle(l.id);o?e.value={id:l.id,title:o.title||l.title,content:o.content||"开始编写您的文章内容...",summary:o.news_points||o.summary||l.summary,status:o.status||l.status||"draft",category:l.category||"",tags:l.tags||[],createdAt:l.createdAt,updatedAt:l.updatedAt}:e.value={...l,content:l.content||"开始编写您的文章内容..."}}_.value=!0}else v.error("文章不存在"),A()}catch(t){console.error("加载文章失败:",t),v.error("加载文章失败"),A()}finally{x.value=!1}}},h=async(a=!0,t="draft")=>{if(!(!a&&!e.value.title.trim()&&!e.value.content.trim())){if(a){if(!e.value.title.trim()){v.warning("请输入文章标题");return}if(!e.value.content.trim()){v.warning("请输入文章内容");return}}b.value=!0;try{const l=O();if(e.value.status=t,e.value.updatedAt=new Date().toLocaleString("zh-CN"),_.value){l.updateArticle(e.value.id,{title:e.value.title,content:e.value.content,summary:e.value.summary,status:e.value.status,category:e.value.category,tags:e.value.tags,updatedAt:e.value.updatedAt}),console.log("准备保存到数据库，文章ID:",e.value.id);const o=await fe(e.value.id,{title:e.value.title,content:e.value.content,news_points:e.value.summary,status:e.value.status});if(console.log("保存结果:",o),o.success)console.log("数据库保存成功!");else throw new Error(o.error)}else{console.log("创建新文章到数据库");const o=await me({topic:e.value.title||"新文章",title:e.value.title,content:e.value.content,news_points:e.value.summary,status:e.value.status});if(o.success)e.value.id=o.data.id,e.value.createdAt=e.value.updatedAt,_.value=!0,console.log("新文章创建成功，ID:",e.value.id),l.addArticle({...e.value,fileName:`article_${e.value.id}.txt`});else throw new Error(o.error)}if(a){const o=t==="published"?"发布":"保存";v.success(`文章${o}成功`)}w.value=!1,C.value=new Date,t==="published"&&A()}catch(l){console.error("保存文章失败:",l),v.error("保存失败，请重试")}finally{b.value=!1}}},A=()=>{const a=N.query.returnQuery;if(a)try{const t=JSON.parse(a);S.push({path:"/articles",query:t})}catch(t){console.error("解析返回查询参数失败:",t),S.push("/articles")}else S.push("/articles")},P=()=>{if(!e.value.content){v.warning("请先输入文章内容");return}const t=e.value.content.replace(/<[^>]*>/g,"").split(/[。！？]/).filter(l=>l.trim().length>10);e.value.summary=t.slice(0,2).join("。")+"。",v.success("摘要生成成功")},W=a=>{e.value.tags.splice(e.value.tags.indexOf(a),1)},X=()=>{D.value=!0},K=()=>{y.value&&!e.value.tags.includes(y.value)&&e.value.tags.push(y.value),D.value=!1,y.value=""},Y=()=>{v.info("预览功能开发中...")};return le([()=>e.value.title,()=>e.value.content,()=>e.value.summary],()=>{w.value=!0},{deep:!0}),ae(()=>{const a=N.params.id;a&&a!=="new"?(_.value=!0,H(a)):e.value.content="<p>开始编写您的文章内容...</p>",M(),document.addEventListener("keydown",U)}),oe(()=>{j(),document.removeEventListener("keydown",U)}),(a,t)=>{const l=m("el-skeleton"),o=m("el-button"),L=m("el-tooltip"),I=m("el-input"),V=m("el-option"),z=m("el-select"),Z=m("el-tag");return i(),c("div",pe,[x.value?(i(),c("div",_e,[u(l,{rows:8,animated:""})])):(i(),c("div",ge,[s("div",ye,[s("div",be,[u(o,{icon:k(ne),onClick:A},{default:r(()=>t[8]||(t[8]=[f(" 返回 ")])),_:1,__:[8]},8,["icon"]),s("span",we,p(_.value?"编辑文章":"创建文章"),1)]),s("div",he,[s("div",Ae,[s("span",ke,p(Q.value)+" 字",1),E.value?(i(),c("span",Ce," 自动保存中... ")):C.value?(i(),c("span",Ve,p(G(C.value)),1)):w.value?(i(),c("span",Se," 有未保存的更改 ")):B("",!0)]),u(o,{onClick:Y,icon:k(ue)},{default:r(()=>t[9]||(t[9]=[f(" 预览 ")])),_:1,__:[9]},8,["icon"]),u(L,{content:"快捷键: Ctrl+S",placement:"bottom"},{default:r(()=>[u(o,{onClick:t[0]||(t[0]=n=>h(!0,"draft")),loading:b.value,icon:k(ie)},{default:r(()=>t[10]||(t[10]=[f(" 保存草稿 ")])),_:1,__:[10]},8,["loading","icon"])]),_:1}),u(L,{content:"快捷键: Ctrl+Enter",placement:"bottom"},{default:r(()=>[u(o,{type:"primary",onClick:t[1]||(t[1]=n=>h(!0,"published")),loading:b.value,icon:k(re)},{default:r(()=>t[11]||(t[11]=[f(" 发布文章 ")])),_:1,__:[11]},8,["loading","icon"])]),_:1})])]),s("div",xe,[s("div",Ee,[s("div",De,[u(I,{modelValue:e.value.title,"onUpdate:modelValue":t[2]||(t[2]=n=>e.value.title=n),placeholder:"请输入文章标题...",size:"large",class:"title-input"},null,8,["modelValue"])]),s("div",Ie,[u(k(de),{content:e.value.content,options:R,contentType:"html",class:"quill-editor","onUpdate:content":t[3]||(t[3]=n=>e.value.content=n)},null,8,["content"])])]),s("div",Te,[s("div",Ne,[t[16]||(t[16]=s("h3",{class:"card-title"},"文章设置",-1)),s("div",Ue,[t[12]||(t[12]=s("label",{class:"setting-label"},"状态",-1)),u(z,{modelValue:e.value.status,"onUpdate:modelValue":t[4]||(t[4]=n=>e.value.status=n),style:{width:"100%"}},{default:r(()=>[u(V,{label:"草稿",value:"draft"}),u(V,{label:"已发布",value:"published"}),u(V,{label:"已归档",value:"archived"})]),_:1},8,["modelValue"])]),s("div",Ke,[t[13]||(t[13]=s("label",{class:"setting-label"},"分类",-1)),u(z,{modelValue:e.value.category,"onUpdate:modelValue":t[5]||(t[5]=n=>e.value.category=n),placeholder:"选择分类",style:{width:"100%"}},{default:r(()=>[(i(),c($,null,q(F,n=>u(V,{key:n.value,label:n.label,value:n.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),s("div",Le,[t[15]||(t[15]=s("label",{class:"setting-label"},"标签",-1)),s("div",ze,[(i(!0),c($,null,q(e.value.tags,n=>(i(),T(Z,{key:n,closable:"",onClose:Pe=>W(n),class:"tag-item"},{default:r(()=>[f(p(n),1)]),_:2},1032,["onClose"]))),128)),D.value?(i(),T(I,{key:0,modelValue:y.value,"onUpdate:modelValue":t[6]||(t[6]=n=>y.value=n),size:"small",onKeyup:ce(K,["enter"]),onBlur:K,class:"tag-input"},null,8,["modelValue"])):(i(),T(o,{key:1,size:"small",onClick:X,class:"add-tag-btn"},{default:r(()=>t[14]||(t[14]=[f(" + 添加标签 ")])),_:1,__:[14]}))])])]),s("div",Be,[s("div",$e,[t[18]||(t[18]=s("h3",{class:"card-title"},"文章摘要",-1)),u(o,{size:"small",text:"",onClick:P},{default:r(()=>t[17]||(t[17]=[f(" 自动生成 ")])),_:1,__:[17]})]),u(I,{modelValue:e.value.summary,"onUpdate:modelValue":t[7]||(t[7]=n=>e.value.summary=n),type:"textarea",rows:4,placeholder:"请输入文章摘要...",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),_.value?(i(),c("div",qe,[t[22]||(t[22]=s("h3",{class:"card-title"},"文章信息",-1)),s("div",Oe,[t[19]||(t[19]=s("span",{class:"info-label"},"创建时间：",-1)),s("span",Qe,p(e.value.createdAt),1)]),s("div",Re,[t[20]||(t[20]=s("span",{class:"info-label"},"更新时间：",-1)),s("span",Fe,p(e.value.updatedAt),1)]),s("div",Me,[t[21]||(t[21]=s("span",{class:"info-label"},"字数统计：",-1)),s("span",je,p(e.value.content.replace(/<[^>]*>/g,"").length)+" 字 ",1)])])):B("",!0)])])]))])}}},Ye=ee(Ge,[["__scopeId","data-v-09199687"]]);export{Ye as default};
