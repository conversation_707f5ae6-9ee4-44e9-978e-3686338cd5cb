import{_ as F,v as p,o as j,K as J,L as G,a as f,b as d,f as i,r as c,e as l,w as u,g as v,u as _,M as H,t as y,I as P,i as W,k as X,Q as Y,d as Z,F as T,m as U,q as E,N as ee,E as r,h as te}from"./index-IFKW_WXq.js";import{u as z,c as le,s as se}from"./articles-CwAJ8VOa.js";const ae={class:"article-edit"},oe={key:0,class:"loading-container"},ne={key:1,class:"editor-container"},ie={class:"editor-toolbar"},ue={class:"toolbar-left"},re={class:"editor-title"},de={class:"toolbar-right"},ce={class:"editor-content"},ve={class:"editor-main"},me={class:"title-section"},pe={class:"content-section"},fe={class:"editor-sidebar"},ge={class:"setting-card"},_e={class:"setting-item"},ye={class:"setting-item"},be={class:"setting-item"},we={class:"tags-container"},Ae={class:"setting-card"},ke={class:"card-header"},he={key:0,class:"setting-card"},Ve={class:"info-item"},Ce={class:"info-value"},xe={class:"info-item"},Ee={class:"info-value"},Se={class:"info-item"},Ie={class:"info-value"},Ne={__name:"ArticleEdit",setup(De){const S=J(),k=te(),t=p({id:null,title:"",content:"",summary:"",status:"draft",category:"",tags:[],createdAt:null,updatedAt:null}),h=p(!1),b=p(!1),g=p(!1),B={theme:"snow",placeholder:"开始写作您的文章...",modules:{toolbar:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{indent:"-1"},{indent:"+1"}],[{size:["small",!1,"large","huge"]}],[{color:[]},{background:[]}],[{align:[]}],["clean"],["link","image"]]}},q=[{label:"名人轶事",value:"celebrity"},{label:"国际局势",value:"international"},{label:"科技动态",value:"technology"},{label:"财经观察",value:"finance"},{label:"体育赛事",value:"sports"},{label:"社会民生",value:"social"},{label:"文化娱乐",value:"entertainment"}],m=p(""),V=p(!1),K=async o=>{if(o){h.value=!0;try{const e=z(),s=e.articles.find(a=>a.id==o);if(s){if(s.content&&s.content!==s.fullSummary)t.value={id:s.id,title:s.title,content:s.content,summary:s.summary,status:s.status||"draft",category:s.category||"",tags:s.tags||[],createdAt:s.createdAt,updatedAt:s.updatedAt};else{const a=await e.loadArticle(s.id);a?t.value={id:s.id,title:a.title||s.title,content:a.content||"开始编写您的文章内容...",summary:a.news_points||a.summary||s.summary,status:a.status||s.status||"draft",category:s.category||"",tags:s.tags||[],createdAt:s.createdAt,updatedAt:s.updatedAt}:t.value={...s,content:s.content||"开始编写您的文章内容..."}}g.value=!0}else r.error("文章不存在"),w()}catch(e){console.error("加载文章失败:",e),r.error("加载文章失败"),w()}finally{h.value=!1}}},C=async(o="draft")=>{if(!t.value.title.trim()){r.warning("请输入文章标题");return}if(!t.value.content.trim()){r.warning("请输入文章内容");return}b.value=!0;try{const e=z();if(t.value.status=o,t.value.updatedAt=new Date().toLocaleString("zh-CN"),g.value){e.updateArticle(t.value.id,{title:t.value.title,content:t.value.content,summary:t.value.summary,status:t.value.status,category:t.value.category,tags:t.value.tags,updatedAt:t.value.updatedAt}),console.log("准备保存到数据库，文章ID:",t.value.id);const a=await se(t.value.id,{title:t.value.title,content:t.value.content,news_points:t.value.summary,status:t.value.status});if(console.log("保存结果:",a),a.success)console.log("数据库保存成功!");else throw new Error(a.error)}else{console.log("创建新文章到数据库");const a=await le({topic:t.value.title||"新文章",title:t.value.title,content:t.value.content,news_points:t.value.summary,status:t.value.status});if(a.success)t.value.id=a.data.id,t.value.createdAt=t.value.updatedAt,g.value=!0,console.log("新文章创建成功，ID:",t.value.id),e.addArticle({...t.value,fileName:`article_${t.value.id}.txt`});else throw new Error(a.error)}const s=o==="published"?"发布":"保存";r.success(`文章${s}成功`),o==="published"&&w()}catch(e){console.error("保存文章失败:",e),r.error("保存失败，请重试")}finally{b.value=!1}},w=()=>{const o=S.query.returnQuery;if(o)try{const e=JSON.parse(o);k.push({path:"/articles",query:e})}catch(e){console.error("解析返回查询参数失败:",e),k.push("/articles")}else k.push("/articles")},L=()=>{if(!t.value.content){r.warning("请先输入文章内容");return}const e=t.value.content.replace(/<[^>]*>/g,"").split(/[。！？]/).filter(s=>s.trim().length>10);t.value.summary=e.slice(0,2).join("。")+"。",r.success("摘要生成成功")},Q=o=>{t.value.tags.splice(t.value.tags.indexOf(o),1)},$=()=>{V.value=!0},I=()=>{m.value&&!t.value.tags.includes(m.value)&&t.value.tags.push(m.value),V.value=!1,m.value=""},O=()=>{r.info("预览功能开发中...")},N=o=>{o.ctrlKey&&o.key==="s"&&(o.preventDefault(),C("draft"),r.success("文章已保存 (Ctrl+S)"))};return j(()=>{const o=S.params.id;o&&o!=="new"?K(o):t.value.content="<p>开始编写您的文章内容...</p>",document.addEventListener("keydown",N)}),G(()=>{document.removeEventListener("keydown",N)}),(o,e)=>{const s=c("el-skeleton"),a=c("el-button"),R=c("el-tooltip"),x=c("el-input"),A=c("el-option"),D=c("el-select"),M=c("el-tag");return d(),f("div",ae,[h.value?(d(),f("div",oe,[i(s,{rows:8,animated:""})])):(d(),f("div",ne,[l("div",ie,[l("div",ue,[i(a,{icon:_(H),onClick:w},{default:u(()=>e[8]||(e[8]=[v(" 返回 ")])),_:1,__:[8]},8,["icon"]),l("span",re,y(g.value?"编辑文章":"创建文章"),1)]),l("div",de,[i(a,{onClick:O,icon:_(P)},{default:u(()=>e[9]||(e[9]=[v(" 预览 ")])),_:1,__:[9]},8,["icon"]),i(R,{content:"快捷键: Ctrl+S",placement:"bottom"},{default:u(()=>[i(a,{onClick:e[0]||(e[0]=n=>C("draft")),loading:b.value,icon:_(W)},{default:u(()=>e[10]||(e[10]=[v(" 保存草稿 ")])),_:1,__:[10]},8,["loading","icon"])]),_:1}),i(a,{type:"primary",onClick:e[1]||(e[1]=n=>C("published")),loading:b.value,icon:_(X)},{default:u(()=>e[11]||(e[11]=[v(" 发布文章 ")])),_:1,__:[11]},8,["loading","icon"])])]),l("div",ce,[l("div",ve,[l("div",me,[i(x,{modelValue:t.value.title,"onUpdate:modelValue":e[2]||(e[2]=n=>t.value.title=n),placeholder:"请输入文章标题...",size:"large",class:"title-input"},null,8,["modelValue"])]),l("div",pe,[i(_(Y),{content:t.value.content,options:B,contentType:"html",class:"quill-editor","onUpdate:content":e[3]||(e[3]=n=>t.value.content=n)},null,8,["content"])])]),l("div",fe,[l("div",ge,[e[16]||(e[16]=l("h3",{class:"card-title"},"文章设置",-1)),l("div",_e,[e[12]||(e[12]=l("label",{class:"setting-label"},"状态",-1)),i(D,{modelValue:t.value.status,"onUpdate:modelValue":e[4]||(e[4]=n=>t.value.status=n),style:{width:"100%"}},{default:u(()=>[i(A,{label:"草稿",value:"draft"}),i(A,{label:"已发布",value:"published"}),i(A,{label:"已归档",value:"archived"})]),_:1},8,["modelValue"])]),l("div",ye,[e[13]||(e[13]=l("label",{class:"setting-label"},"分类",-1)),i(D,{modelValue:t.value.category,"onUpdate:modelValue":e[5]||(e[5]=n=>t.value.category=n),placeholder:"选择分类",style:{width:"100%"}},{default:u(()=>[(d(),f(T,null,U(q,n=>i(A,{key:n.value,label:n.label,value:n.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),l("div",be,[e[15]||(e[15]=l("label",{class:"setting-label"},"标签",-1)),l("div",we,[(d(!0),f(T,null,U(t.value.tags,n=>(d(),E(M,{key:n,closable:"",onClose:Te=>Q(n),class:"tag-item"},{default:u(()=>[v(y(n),1)]),_:2},1032,["onClose"]))),128)),V.value?(d(),E(x,{key:0,modelValue:m.value,"onUpdate:modelValue":e[6]||(e[6]=n=>m.value=n),size:"small",onKeyup:ee(I,["enter"]),onBlur:I,class:"tag-input"},null,8,["modelValue"])):(d(),E(a,{key:1,size:"small",onClick:$,class:"add-tag-btn"},{default:u(()=>e[14]||(e[14]=[v(" + 添加标签 ")])),_:1,__:[14]}))])])]),l("div",Ae,[l("div",ke,[e[18]||(e[18]=l("h3",{class:"card-title"},"文章摘要",-1)),i(a,{size:"small",text:"",onClick:L},{default:u(()=>e[17]||(e[17]=[v(" 自动生成 ")])),_:1,__:[17]})]),i(x,{modelValue:t.value.summary,"onUpdate:modelValue":e[7]||(e[7]=n=>t.value.summary=n),type:"textarea",rows:4,placeholder:"请输入文章摘要...",maxlength:"200","show-word-limit":""},null,8,["modelValue"])]),g.value?(d(),f("div",he,[e[22]||(e[22]=l("h3",{class:"card-title"},"文章信息",-1)),l("div",Ve,[e[19]||(e[19]=l("span",{class:"info-label"},"创建时间：",-1)),l("span",Ce,y(t.value.createdAt),1)]),l("div",xe,[e[20]||(e[20]=l("span",{class:"info-label"},"更新时间：",-1)),l("span",Ee,y(t.value.updatedAt),1)]),l("div",Se,[e[21]||(e[21]=l("span",{class:"info-label"},"字数统计：",-1)),l("span",Ie,y(t.value.content.replace(/<[^>]*>/g,"").length)+" 字 ",1)])])):Z("",!0)])])]))])}}},Be=F(Ne,[["__scopeId","data-v-0aca693f"]]);export{Be as default};
