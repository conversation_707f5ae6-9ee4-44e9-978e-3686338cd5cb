import{_ as we,v as r,c as q,x as K,K as be,o as ke,L as Ae,M as Se,a as f,b as p,f as s,r as v,e as l,w as i,g as _,u as c,N as Ce,t as d,q as O,d as Z,i as b,O as Ve,F as Q,m as R,y as Te,I as F,k as ee,Q as xe,P as ze,E as g,h as Le,J as Ee}from"./index-BVQ-x3Kw.js";import{u as te,c as De,s as Be}from"./articles-BDQZBnjv.js";const Ie={class:"article-edit"},Me={key:0,class:"loading-container"},Ne={key:1,class:"editor-container"},Ue={class:"editor-header"},$e={class:"header-left"},qe={class:"title-section"},Ke={class:"page-title"},Oe={class:"status-info"},Qe={class:"auto-save-status"},Re={class:"header-right"},Fe={class:"stats-bar"},He={class:"stats-item"},Je={class:"stats-item"},Pe={class:"stats-item"},We={class:"editor-content"},je={key:0,class:"edit-mode"},Ge={class:"editor-main"},Xe={class:"title-section"},Ye={class:"content-section"},Ze={key:1,class:"preview-mode"},et={class:"preview-container"},tt={class:"preview-title"},lt={class:"preview-meta"},at=["innerHTML"],st={class:"editor-sidebar"},ot={class:"setting-card"},nt={class:"card-title"},it={class:"stats-grid"},ut={class:"stat-item"},rt={class:"stat-value"},dt={class:"stat-item"},ct={class:"stat-value"},vt={class:"stat-item"},pt={class:"stat-value"},_t={class:"setting-card"},mt={class:"card-title"},ft={class:"setting-item"},gt={class:"setting-item"},ht={class:"setting-item"},yt={class:"tags-container"},wt={class:"tag-input-section"},bt={class:"setting-item"},kt={key:0,class:"setting-card"},At={class:"card-title"},St={class:"info-grid"},Ct={class:"info-item"},Vt={class:"info-value"},Tt={class:"info-item"},xt={class:"info-value"},zt={__name:"ArticleEdit",setup(Lt){const H=Ae(),E=Le(),t=r({id:null,title:"",content:"",summary:"",status:"draft",category:"",tags:[],createdAt:null,updatedAt:null}),D=r(!1),k=r(!1),A=r(!1),V=r(!1),B=r(null),m=r(!1),y=r(!1),S=r(0),I=r(0),M=r(0),C=r(null),w=r(""),J=r(["时政","娱乐","科技","体育","社会","国际","财经","其他"]),N=r([]),le=r([{name:"新闻报道",title:"新闻标题",content:"<h2>导语</h2><p>简要概述新闻要点...</p><h2>正文</h2><p>详细报道内容...</p><h2>结语</h2><p>总结或展望...</p>"},{name:"观点评论",title:"评论标题",content:"<h2>引言</h2><p>引出话题...</p><h2>观点阐述</h2><p>详细论述观点...</p><h2>总结</h2><p>总结观点...</p>"},{name:"深度分析",title:"分析标题",content:"<h2>背景介绍</h2><p>介绍相关背景...</p><h2>问题分析</h2><p>深入分析问题...</p><h2>解决方案</h2><p>提出解决方案...</p>"}]),ae={theme:"snow",placeholder:"开始写作您的文章...",modules:{toolbar:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{indent:"-1"},{indent:"+1"}],[{size:["small",!1,"large","huge"]}],[{color:[]},{background:[]}],[{align:[]}],["clean"],["link","image"]]}},se=[{label:"时政",value:"时政"},{label:"娱乐",value:"娱乐"},{label:"科技",value:"科技"},{label:"体育",value:"体育"},{label:"社会",value:"社会"},{label:"国际",value:"国际"},{label:"财经",value:"财经"},{label:"其他",value:"其他"}];r(!1);const oe=q(()=>A.value?`编辑文章 - ${t.value.title||"无标题"}`:"创建新文章"),T=q(()=>t.value.title.trim()&&t.value.content.trim()&&!k.value),ne=q(()=>V.value?"正在自动保存...":B.value?`上次保存: ${B.value.toLocaleTimeString()}`:"未保存"),U=()=>{const a=t.value.content.replace(/<[^>]*>/g,"").trim();S.value=a.length,I.value=a.replace(/\s/g,"").length,M.value=Math.ceil(S.value/200)},ie=()=>{C.value&&clearInterval(C.value),C.value=setInterval(async()=>{m.value&&T.value&&await ue()},3e4)},ue=async()=>{if(T.value){V.value=!0;try{await x(!1),B.value=new Date,m.value=!1}catch(a){console.error("自动保存失败:",a)}finally{V.value=!1}}},re=a=>{t.value.title=a.title,t.value.content=a.content,m.value=!0,U(),g.success(`已应用模板: ${a.name}`)},de=()=>{y.value=!y.value},P=a=>{a?N.value=J.value.filter(e=>e.toLowerCase().includes(a.toLowerCase())):N.value=J.value},$=()=>{const a=w.value.trim();a&&!t.value.tags.includes(a)&&(t.value.tags.push(a),w.value="",m.value=!0)},ce=a=>{const e=t.value.tags.indexOf(a);e>-1&&(t.value.tags.splice(e,1),m.value=!0)},ve=async a=>{if(a){D.value=!0;try{const e=te(),o=e.articles.find(u=>u.id==a);if(o){if(o.content&&o.content!==o.fullSummary)t.value={id:o.id,title:o.title,content:o.content,summary:o.summary,status:o.status||"draft",category:o.category||"",tags:o.tags||[],createdAt:o.createdAt,updatedAt:o.updatedAt};else{const u=await e.loadArticle(o.id);u?t.value={id:o.id,title:u.title||o.title,content:u.content||"开始编写您的文章内容...",summary:u.news_points||u.summary||o.summary,status:u.status||o.status||"draft",category:o.category||"",tags:o.tags||[],createdAt:o.createdAt,updatedAt:o.updatedAt}:t.value={...o,content:o.content||"开始编写您的文章内容..."}}A.value=!0}else g.error("文章不存在"),z()}catch(e){console.error("加载文章失败:",e),g.error("加载文章失败"),z()}finally{D.value=!1}}},x=async(a="draft")=>{if(!t.value.title.trim()){g.warning("请输入文章标题");return}if(!t.value.content.trim()){g.warning("请输入文章内容");return}k.value=!0;try{const e=te();if(t.value.status=a,t.value.updatedAt=new Date().toLocaleString("zh-CN"),A.value){e.updateArticle(t.value.id,{title:t.value.title,content:t.value.content,summary:t.value.summary,status:t.value.status,category:t.value.category,tags:t.value.tags,updatedAt:t.value.updatedAt}),console.log("准备保存到数据库，文章ID:",t.value.id);const u=await Be(t.value.id,{title:t.value.title,content:t.value.content,news_points:t.value.summary,status:t.value.status});if(console.log("保存结果:",u),u.success)console.log("数据库保存成功!");else throw new Error(u.error)}else{console.log("创建新文章到数据库");const u=await De({topic:t.value.title||"新文章",title:t.value.title,content:t.value.content,news_points:t.value.summary,status:t.value.status});if(u.success)t.value.id=u.data.id,t.value.createdAt=t.value.updatedAt,A.value=!0,console.log("新文章创建成功，ID:",t.value.id),e.addArticle({...t.value,fileName:`article_${t.value.id}.txt`});else throw new Error(u.error)}const o=a==="published"?"发布":"保存";g.success(`文章${o}成功`),a==="published"&&z()}catch(e){console.error("保存文章失败:",e),g.error("保存失败，请重试")}finally{k.value=!1}},z=()=>{const a=H.query.returnQuery;if(a)try{const e=JSON.parse(a);E.push({path:"/articles",query:e})}catch(e){console.error("解析返回查询参数失败:",e),E.push("/articles")}else E.push("/articles")},W=a=>{a.ctrlKey&&a.key==="s"&&(a.preventDefault(),x("draft"),g.success("文章已保存 (Ctrl+S)"))};return K(()=>t.value.content,()=>{m.value=!0,U()},{deep:!0}),K(()=>t.value.title,()=>{m.value=!0}),K(()=>w.value,a=>{P(a)}),be((a,e,o)=>{m.value?Ee.confirm("您有未保存的更改，确定要离开吗？","确认离开",{confirmButtonText:"离开",cancelButtonText:"取消",type:"warning"}).then(()=>{o()}).catch(()=>{o(!1)}):o()}),ke(()=>{const a=H.params.id;a&&a!=="new"?ve(a):(t.value.content="<p>开始编写您的文章内容...</p>",U()),document.addEventListener("keydown",W),ie(),P("")}),Se(()=>{document.removeEventListener("keydown",W),C.value&&clearInterval(C.value)}),(a,e)=>{const o=v("el-skeleton"),u=v("el-button"),j=v("el-tag"),h=v("el-icon"),pe=v("el-dropdown-item"),_e=v("el-dropdown-menu"),me=v("el-dropdown"),fe=v("el-tooltip"),ge=v("el-button-group"),G=v("el-input"),L=v("el-option"),X=v("el-select"),he=v("el-autocomplete");return p(),f("div",Ie,[D.value?(p(),f("div",Me,[s(o,{rows:8,animated:""})])):(p(),f("div",Ne,[l("div",Ue,[l("div",$e,[s(u,{icon:c(Ce),onClick:z,size:"large"},{default:i(()=>e[9]||(e[9]=[_(" 返回 ")])),_:1,__:[9]},8,["icon"]),l("div",qe,[l("h1",Ke,d(oe.value),1),l("div",Oe,[l("span",Qe,d(ne.value),1),m.value?(p(),O(j,{key:0,type:"warning",size:"small"},{default:i(()=>e[10]||(e[10]=[_("未保存")])),_:1,__:[10]})):Z("",!0)])])]),l("div",Re,[s(me,{trigger:"click",onCommand:re},{dropdown:i(()=>[s(_e,null,{default:i(()=>[(p(!0),f(Q,null,R(le.value,n=>(p(),O(pe,{key:n.name,command:n},{default:i(()=>[_(d(n.name),1)]),_:2},1032,["command"]))),128))]),_:1})]),default:i(()=>[s(u,{icon:c(b),size:"large"},{default:i(()=>[e[11]||(e[11]=_(" 模板 ")),s(h,{class:"el-icon--right"},{default:i(()=>[s(c(Ve))]),_:1})]),_:1,__:[11]},8,["icon"])]),_:1}),s(u,{onClick:de,icon:y.value?c(Te):c(F),type:y.value?"primary":"default",size:"large"},{default:i(()=>[_(d(y.value?"编辑":"预览"),1)]),_:1},8,["icon","type"]),s(ge,null,{default:i(()=>[s(fe,{content:"快捷键: Ctrl+S",placement:"bottom"},{default:i(()=>[s(u,{onClick:e[0]||(e[0]=n=>x("draft")),loading:k.value||V.value,icon:c(b),size:"large",disabled:!T.value},{default:i(()=>e[12]||(e[12]=[_(" 保存草稿 ")])),_:1,__:[12]},8,["loading","icon","disabled"])]),_:1}),s(u,{type:"primary",onClick:e[1]||(e[1]=n=>x("published")),loading:k.value,icon:c(ee),size:"large",disabled:!T.value},{default:i(()=>e[13]||(e[13]=[_(" 发布文章 ")])),_:1,__:[13]},8,["loading","icon","disabled"])]),_:1})])]),l("div",Fe,[l("div",He,[s(h,null,{default:i(()=>[s(c(b))]),_:1}),l("span",null,"字数: "+d(S.value),1)]),l("div",Je,[s(h,null,{default:i(()=>[s(c(F))]),_:1}),l("span",null,"预计阅读: "+d(M.value)+"分钟",1)]),l("div",Pe,[s(h,null,{default:i(()=>[s(c(b))]),_:1}),l("span",null,"字符: "+d(I.value),1)])]),l("div",We,[y.value?(p(),f("div",Ze,[l("div",et,[l("h1",tt,d(t.value.title||"无标题"),1),l("div",lt,[l("span",null,"分类: "+d(t.value.category||"未分类"),1),l("span",null,"状态: "+d(t.value.status==="draft"?"草稿":"已发布"),1),l("span",null,"字数: "+d(S.value),1)]),l("div",{class:"preview-content",innerHTML:t.value.content},null,8,at)])])):(p(),f("div",je,[l("div",Ge,[l("div",Xe,[s(G,{modelValue:t.value.title,"onUpdate:modelValue":e[2]||(e[2]=n=>t.value.title=n),placeholder:"请输入文章标题...",size:"large",class:"title-input",maxlength:"100","show-word-limit":""},null,8,["modelValue"])]),l("div",Ye,[s(c(xe),{content:t.value.content,options:ae,contentType:"html",class:"quill-editor","onUpdate:content":e[3]||(e[3]=n=>t.value.content=n)},null,8,["content"])])])])),l("div",st,[l("div",ot,[l("h3",nt,[s(h,null,{default:i(()=>[s(c(b))]),_:1}),e[14]||(e[14]=_(" 文章统计 "))]),l("div",it,[l("div",ut,[e[15]||(e[15]=l("span",{class:"stat-label"},"字数",-1)),l("span",rt,d(S.value),1)]),l("div",dt,[e[16]||(e[16]=l("span",{class:"stat-label"},"字符",-1)),l("span",ct,d(I.value),1)]),l("div",vt,[e[17]||(e[17]=l("span",{class:"stat-label"},"阅读时间",-1)),l("span",pt,d(M.value)+"分钟",1)])])]),l("div",_t,[l("h3",mt,[s(h,null,{default:i(()=>[s(c(b))]),_:1}),e[18]||(e[18]=_(" 文章设置 "))]),l("div",ft,[e[19]||(e[19]=l("label",{class:"setting-label"},"状态",-1)),s(X,{modelValue:t.value.status,"onUpdate:modelValue":e[4]||(e[4]=n=>t.value.status=n),style:{width:"100%"},size:"large"},{default:i(()=>[s(L,{label:"草稿",value:"draft"}),s(L,{label:"已发布",value:"published"}),s(L,{label:"已归档",value:"archived"})]),_:1},8,["modelValue"])]),l("div",gt,[e[20]||(e[20]=l("label",{class:"setting-label"},"分类",-1)),s(X,{modelValue:t.value.category,"onUpdate:modelValue":e[5]||(e[5]=n=>t.value.category=n),placeholder:"选择分类",style:{width:"100%"},size:"large"},{default:i(()=>[(p(),f(Q,null,R(se,n=>s(L,{key:n.value,label:n.label,value:n.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),l("div",ht,[e[22]||(e[22]=l("label",{class:"setting-label"},"标签",-1)),l("div",yt,[(p(!0),f(Q,null,R(t.value.tags,n=>(p(),O(j,{key:n,closable:"",onClose:Y=>ce(n),class:"tag-item",size:"large"},{default:i(()=>[_(d(n),1)]),_:2},1032,["onClose"]))),128))]),l("div",wt,[s(he,{modelValue:w.value,"onUpdate:modelValue":e[6]||(e[6]=n=>w.value=n),"fetch-suggestions":(n,Y)=>Y(N.value.map(ye=>({value:ye}))),placeholder:"输入标签名称",onKeyup:ze($,["enter"]),onSelect:e[7]||(e[7]=n=>{w.value=n.value,$()}),style:{width:"100%"},size:"large"},{append:i(()=>[s(u,{onClick:$,icon:c(ee)},{default:i(()=>e[21]||(e[21]=[_("添加")])),_:1,__:[21]},8,["icon"])]),_:1},8,["modelValue","fetch-suggestions"])])]),l("div",bt,[e[23]||(e[23]=l("label",{class:"setting-label"},"文章摘要",-1)),s(G,{modelValue:t.value.summary,"onUpdate:modelValue":e[8]||(e[8]=n=>t.value.summary=n),type:"textarea",rows:4,placeholder:"请输入文章摘要...",maxlength:"200","show-word-limit":"",resize:"none"},null,8,["modelValue"])])]),A.value?(p(),f("div",kt,[l("h3",At,[s(h,null,{default:i(()=>[s(c(F))]),_:1}),e[24]||(e[24]=_(" 文章信息 "))]),l("div",St,[l("div",Ct,[e[25]||(e[25]=l("span",{class:"info-label"},"创建时间",-1)),l("span",Vt,d(new Date(t.value.createdAt).toLocaleString()),1)]),l("div",Tt,[e[26]||(e[26]=l("span",{class:"info-label"},"更新时间",-1)),l("span",xt,d(new Date(t.value.updatedAt).toLocaleString()),1)])])])):Z("",!0)])])]))])}}},Bt=we(zt,[["__scopeId","data-v-52c8ff6c"]]);export{Bt as default};
