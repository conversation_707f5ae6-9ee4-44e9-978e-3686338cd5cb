import{O as Ye}from"./index-IFKW_WXq.js";function xe(e,t){return function(){return e.apply(t,arguments)}}const{toString:et}=Object.prototype,{getPrototypeOf:ue}=Object,{iterator:v,toStringTag:Ne}=Symbol,Q=(e=>t=>{const r=et.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),x=e=>(e=e.toLowerCase(),t=>Q(t)===e),X=e=>t=>typeof t===e,{isArray:B}=Array,q=X("undefined");function tt(e){return e!==null&&!q(e)&&e.constructor!==null&&!q(e.constructor)&&T(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Pe=x("ArrayBuffer");function rt(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Pe(e.buffer),t}const nt=X("string"),T=X("function"),Le=X("number"),G=e=>e!==null&&typeof e=="object",st=e=>e===!0||e===!1,$=e=>{if(Q(e)!=="object")return!1;const t=ue(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Ne in e)&&!(v in e)},ot=x("Date"),it=x("File"),at=x("Blob"),ct=x("FileList"),lt=e=>G(e)&&T(e.pipe),ut=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||T(e.append)&&((t=Q(e))==="formdata"||t==="object"&&T(e.toString)&&e.toString()==="[object FormData]"))},ft=x("URLSearchParams"),[dt,ht,pt,mt]=["ReadableStream","Request","Response","Headers"].map(x),yt=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function I(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let n,s;if(typeof e!="object"&&(e=[e]),B(e))for(n=0,s=e.length;n<s;n++)t.call(null,e[n],n,e);else{const o=r?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let c;for(n=0;n<i;n++)c=o[n],t.call(null,e[c],c,e)}}function Fe(e,t){t=t.toLowerCase();const r=Object.keys(e);let n=r.length,s;for(;n-- >0;)if(s=r[n],t===s.toLowerCase())return s;return null}const D=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,De=e=>!q(e)&&e!==D;function se(){const{caseless:e}=De(this)&&this||{},t={},r=(n,s)=>{const o=e&&Fe(t,s)||s;$(t[o])&&$(n)?t[o]=se(t[o],n):$(n)?t[o]=se({},n):B(n)?t[o]=n.slice():t[o]=n};for(let n=0,s=arguments.length;n<s;n++)arguments[n]&&I(arguments[n],r);return t}const wt=(e,t,r,{allOwnKeys:n}={})=>(I(t,(s,o)=>{r&&T(s)?e[o]=xe(s,r):e[o]=s},{allOwnKeys:n}),e),bt=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),gt=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},St=(e,t,r,n)=>{let s,o,i;const c={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)i=s[o],(!n||n(i,e,t))&&!c[i]&&(t[i]=e[i],c[i]=!0);e=r!==!1&&ue(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},Et=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},At=e=>{if(!e)return null;if(B(e))return e;let t=e.length;if(!Le(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},Rt=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&ue(Uint8Array)),Ot=(e,t)=>{const n=(e&&e[v]).call(e);let s;for(;(s=n.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},Tt=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},Ct=x("HTMLFormElement"),xt=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,s){return n.toUpperCase()+s}),pe=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),Nt=x("RegExp"),_e=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};I(r,(s,o)=>{let i;(i=t(s,o,e))!==!1&&(n[o]=i||s)}),Object.defineProperties(e,n)},Pt=e=>{_e(e,(t,r)=>{if(T(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(T(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Lt=(e,t)=>{const r={},n=s=>{s.forEach(o=>{r[o]=!0})};return B(e)?n(e):n(String(e).split(t)),r},Ft=()=>{},Dt=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function _t(e){return!!(e&&T(e.append)&&e[Ne]==="FormData"&&e[v])}const Ut=e=>{const t=new Array(10),r=(n,s)=>{if(G(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[s]=n;const o=B(n)?[]:{};return I(n,(i,c)=>{const f=r(i,s+1);!q(f)&&(o[c]=f)}),t[s]=void 0,o}}return n};return r(e,0)},Bt=x("AsyncFunction"),kt=e=>e&&(G(e)||T(e))&&T(e.then)&&T(e.catch),Ue=((e,t)=>e?setImmediate:t?((r,n)=>(D.addEventListener("message",({source:s,data:o})=>{s===D&&o===r&&n.length&&n.shift()()},!1),s=>{n.push(s),D.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",T(D.postMessage)),jt=typeof queueMicrotask<"u"?queueMicrotask.bind(D):typeof process<"u"&&process.nextTick||Ue,qt=e=>e!=null&&T(e[v]),a={isArray:B,isArrayBuffer:Pe,isBuffer:tt,isFormData:ut,isArrayBufferView:rt,isString:nt,isNumber:Le,isBoolean:st,isObject:G,isPlainObject:$,isReadableStream:dt,isRequest:ht,isResponse:pt,isHeaders:mt,isUndefined:q,isDate:ot,isFile:it,isBlob:at,isRegExp:Nt,isFunction:T,isStream:lt,isURLSearchParams:ft,isTypedArray:Rt,isFileList:ct,forEach:I,merge:se,extend:wt,trim:yt,stripBOM:bt,inherits:gt,toFlatObject:St,kindOf:Q,kindOfTest:x,endsWith:Et,toArray:At,forEachEntry:Ot,matchAll:Tt,isHTMLForm:Ct,hasOwnProperty:pe,hasOwnProp:pe,reduceDescriptors:_e,freezeMethods:Pt,toObjectSet:Lt,toCamelCase:xt,noop:Ft,toFiniteNumber:Dt,findKey:Fe,global:D,isContextDefined:De,isSpecCompliantForm:_t,toJSONObject:Ut,isAsyncFn:Bt,isThenable:kt,setImmediate:Ue,asap:jt,isIterable:qt};function m(e,t,r,n,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),s&&(this.response=s,this.status=s.status?s.status:null)}a.inherits(m,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:a.toJSONObject(this.config),code:this.code,status:this.status}}});const Be=m.prototype,ke={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ke[e]={value:e}});Object.defineProperties(m,ke);Object.defineProperty(Be,"isAxiosError",{value:!0});m.from=(e,t,r,n,s,o)=>{const i=Object.create(Be);return a.toFlatObject(e,i,function(f){return f!==Error.prototype},c=>c!=="isAxiosError"),m.call(i,e.message,t,r,n,s),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const It=null;function oe(e){return a.isPlainObject(e)||a.isArray(e)}function je(e){return a.endsWith(e,"[]")?e.slice(0,-2):e}function me(e,t,r){return e?e.concat(t).map(function(s,o){return s=je(s),!r&&o?"["+s+"]":s}).join(r?".":""):t}function Ht(e){return a.isArray(e)&&!e.some(oe)}const Mt=a.toFlatObject(a,{},null,function(t){return/^is[A-Z]/.test(t)});function Z(e,t,r){if(!a.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=a.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,p){return!a.isUndefined(p[y])});const n=r.metaTokens,s=r.visitor||u,o=r.dots,i=r.indexes,f=(r.Blob||typeof Blob<"u"&&Blob)&&a.isSpecCompliantForm(t);if(!a.isFunction(s))throw new TypeError("visitor must be a function");function l(d){if(d===null)return"";if(a.isDate(d))return d.toISOString();if(a.isBoolean(d))return d.toString();if(!f&&a.isBlob(d))throw new m("Blob is not supported. Use a Buffer instead.");return a.isArrayBuffer(d)||a.isTypedArray(d)?f&&typeof Blob=="function"?new Blob([d]):Buffer.from(d):d}function u(d,y,p){let b=d;if(d&&!p&&typeof d=="object"){if(a.endsWith(y,"{}"))y=n?y:y.slice(0,-2),d=JSON.stringify(d);else if(a.isArray(d)&&Ht(d)||(a.isFileList(d)||a.endsWith(y,"[]"))&&(b=a.toArray(d)))return y=je(y),b.forEach(function(A,P){!(a.isUndefined(A)||A===null)&&t.append(i===!0?me([y],P,o):i===null?y:y+"[]",l(A))}),!1}return oe(d)?!0:(t.append(me(p,y,o),l(d)),!1)}const h=[],w=Object.assign(Mt,{defaultVisitor:u,convertValue:l,isVisitable:oe});function S(d,y){if(!a.isUndefined(d)){if(h.indexOf(d)!==-1)throw Error("Circular reference detected in "+y.join("."));h.push(d),a.forEach(d,function(b,E){(!(a.isUndefined(b)||b===null)&&s.call(t,b,a.isString(E)?E.trim():E,y,w))===!0&&S(b,y?y.concat(E):[E])}),h.pop()}}if(!a.isObject(e))throw new TypeError("data must be an object");return S(e),t}function ye(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function fe(e,t){this._pairs=[],e&&Z(e,this,t)}const qe=fe.prototype;qe.append=function(t,r){this._pairs.push([t,r])};qe.toString=function(t){const r=t?function(n){return t.call(this,n,ye)}:ye;return this._pairs.map(function(s){return r(s[0])+"="+r(s[1])},"").join("&")};function zt(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ie(e,t,r){if(!t)return e;const n=r&&r.encode||zt;a.isFunction(r)&&(r={serialize:r});const s=r&&r.serialize;let o;if(s?o=s(t,r):o=a.isURLSearchParams(t)?t.toString():new fe(t,r).toString(n),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class we{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){a.forEach(this.handlers,function(n){n!==null&&t(n)})}}const He={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},$t=typeof URLSearchParams<"u"?URLSearchParams:fe,Jt=typeof FormData<"u"?FormData:null,Vt=typeof Blob<"u"?Blob:null,Wt={isBrowser:!0,classes:{URLSearchParams:$t,FormData:Jt,Blob:Vt},protocols:["http","https","file","blob","url","data"]},de=typeof window<"u"&&typeof document<"u",ie=typeof navigator=="object"&&navigator||void 0,Kt=de&&(!ie||["ReactNative","NativeScript","NS"].indexOf(ie.product)<0),vt=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Qt=de&&window.location.href||"http://localhost",Xt=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:de,hasStandardBrowserEnv:Kt,hasStandardBrowserWebWorkerEnv:vt,navigator:ie,origin:Qt},Symbol.toStringTag,{value:"Module"})),R={...Xt,...Wt};function Gt(e,t){return Z(e,new R.classes.URLSearchParams,Object.assign({visitor:function(r,n,s,o){return R.isNode&&a.isBuffer(r)?(this.append(n,r.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Zt(e){return a.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Yt(e){const t={},r=Object.keys(e);let n;const s=r.length;let o;for(n=0;n<s;n++)o=r[n],t[o]=e[o];return t}function Me(e){function t(r,n,s,o){let i=r[o++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),f=o>=r.length;return i=!i&&a.isArray(s)?s.length:i,f?(a.hasOwnProp(s,i)?s[i]=[s[i],n]:s[i]=n,!c):((!s[i]||!a.isObject(s[i]))&&(s[i]=[]),t(r,n,s[i],o)&&a.isArray(s[i])&&(s[i]=Yt(s[i])),!c)}if(a.isFormData(e)&&a.isFunction(e.entries)){const r={};return a.forEachEntry(e,(n,s)=>{t(Zt(n),s,r,0)}),r}return null}function er(e,t,r){if(a.isString(e))try{return(t||JSON.parse)(e),a.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const H={transitional:He,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const n=r.getContentType()||"",s=n.indexOf("application/json")>-1,o=a.isObject(t);if(o&&a.isHTMLForm(t)&&(t=new FormData(t)),a.isFormData(t))return s?JSON.stringify(Me(t)):t;if(a.isArrayBuffer(t)||a.isBuffer(t)||a.isStream(t)||a.isFile(t)||a.isBlob(t)||a.isReadableStream(t))return t;if(a.isArrayBufferView(t))return t.buffer;if(a.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Gt(t,this.formSerializer).toString();if((c=a.isFileList(t))||n.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return Z(c?{"files[]":t}:t,f&&new f,this.formSerializer)}}return o||s?(r.setContentType("application/json",!1),er(t)):t}],transformResponse:[function(t){const r=this.transitional||H.transitional,n=r&&r.forcedJSONParsing,s=this.responseType==="json";if(a.isResponse(t)||a.isReadableStream(t))return t;if(t&&a.isString(t)&&(n&&!this.responseType||s)){const i=!(r&&r.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(c){if(i)throw c.name==="SyntaxError"?m.from(c,m.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:R.classes.FormData,Blob:R.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};a.forEach(["delete","get","head","post","put","patch"],e=>{H.headers[e]={}});const tr=a.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),rr=e=>{const t={};let r,n,s;return e&&e.split(`
`).forEach(function(i){s=i.indexOf(":"),r=i.substring(0,s).trim().toLowerCase(),n=i.substring(s+1).trim(),!(!r||t[r]&&tr[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},be=Symbol("internals");function j(e){return e&&String(e).trim().toLowerCase()}function J(e){return e===!1||e==null?e:a.isArray(e)?e.map(J):String(e)}function nr(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const sr=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function te(e,t,r,n,s){if(a.isFunction(n))return n.call(this,t,r);if(s&&(t=r),!!a.isString(t)){if(a.isString(n))return t.indexOf(n)!==-1;if(a.isRegExp(n))return n.test(t)}}function or(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function ir(e,t){const r=a.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(s,o,i){return this[n].call(this,t,s,o,i)},configurable:!0})})}let C=class{constructor(t){t&&this.set(t)}set(t,r,n){const s=this;function o(c,f,l){const u=j(f);if(!u)throw new Error("header name must be a non-empty string");const h=a.findKey(s,u);(!h||s[h]===void 0||l===!0||l===void 0&&s[h]!==!1)&&(s[h||f]=J(c))}const i=(c,f)=>a.forEach(c,(l,u)=>o(l,u,f));if(a.isPlainObject(t)||t instanceof this.constructor)i(t,r);else if(a.isString(t)&&(t=t.trim())&&!sr(t))i(rr(t),r);else if(a.isObject(t)&&a.isIterable(t)){let c={},f,l;for(const u of t){if(!a.isArray(u))throw TypeError("Object iterator must return a key-value pair");c[l=u[0]]=(f=c[l])?a.isArray(f)?[...f,u[1]]:[f,u[1]]:u[1]}i(c,r)}else t!=null&&o(r,t,n);return this}get(t,r){if(t=j(t),t){const n=a.findKey(this,t);if(n){const s=this[n];if(!r)return s;if(r===!0)return nr(s);if(a.isFunction(r))return r.call(this,s,n);if(a.isRegExp(r))return r.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=j(t),t){const n=a.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||te(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let s=!1;function o(i){if(i=j(i),i){const c=a.findKey(n,i);c&&(!r||te(n,n[c],c,r))&&(delete n[c],s=!0)}}return a.isArray(t)?t.forEach(o):o(t),s}clear(t){const r=Object.keys(this);let n=r.length,s=!1;for(;n--;){const o=r[n];(!t||te(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const r=this,n={};return a.forEach(this,(s,o)=>{const i=a.findKey(n,o);if(i){r[i]=J(s),delete r[o];return}const c=t?or(o):String(o).trim();c!==o&&delete r[o],r[c]=J(s),n[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return a.forEach(this,(n,s)=>{n!=null&&n!==!1&&(r[s]=t&&a.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(s=>n.set(s)),n}static accessor(t){const n=(this[be]=this[be]={accessors:{}}).accessors,s=this.prototype;function o(i){const c=j(i);n[c]||(ir(s,i),n[c]=!0)}return a.isArray(t)?t.forEach(o):o(t),this}};C.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);a.reduceDescriptors(C.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});a.freezeMethods(C);function re(e,t){const r=this||H,n=t||r,s=C.from(n.headers);let o=n.data;return a.forEach(e,function(c){o=c.call(r,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function ze(e){return!!(e&&e.__CANCEL__)}function k(e,t,r){m.call(this,e??"canceled",m.ERR_CANCELED,t,r),this.name="CanceledError"}a.inherits(k,m,{__CANCEL__:!0});function $e(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new m("Request failed with status code "+r.status,[m.ERR_BAD_REQUEST,m.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function ar(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function cr(e,t){e=e||10;const r=new Array(e),n=new Array(e);let s=0,o=0,i;return t=t!==void 0?t:1e3,function(f){const l=Date.now(),u=n[o];i||(i=l),r[s]=f,n[s]=l;let h=o,w=0;for(;h!==s;)w+=r[h++],h=h%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),l-i<t)return;const S=u&&l-u;return S?Math.round(w*1e3/S):void 0}}function lr(e,t){let r=0,n=1e3/t,s,o;const i=(l,u=Date.now())=>{r=u,s=null,o&&(clearTimeout(o),o=null),e.apply(null,l)};return[(...l)=>{const u=Date.now(),h=u-r;h>=n?i(l,u):(s=l,o||(o=setTimeout(()=>{o=null,i(s)},n-h)))},()=>s&&i(s)]}const W=(e,t,r=3)=>{let n=0;const s=cr(50,250);return lr(o=>{const i=o.loaded,c=o.lengthComputable?o.total:void 0,f=i-n,l=s(f),u=i<=c;n=i;const h={loaded:i,total:c,progress:c?i/c:void 0,bytes:f,rate:l||void 0,estimated:l&&c&&u?(c-i)/l:void 0,event:o,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(h)},r)},ge=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},Se=e=>(...t)=>a.asap(()=>e(...t)),ur=R.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,R.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(R.origin),R.navigator&&/(msie|trident)/i.test(R.navigator.userAgent)):()=>!0,fr=R.hasStandardBrowserEnv?{write(e,t,r,n,s,o){const i=[e+"="+encodeURIComponent(t)];a.isNumber(r)&&i.push("expires="+new Date(r).toGMTString()),a.isString(n)&&i.push("path="+n),a.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function dr(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function hr(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Je(e,t,r){let n=!dr(t);return e&&(n||r==!1)?hr(e,t):t}const Ee=e=>e instanceof C?{...e}:e;function U(e,t){t=t||{};const r={};function n(l,u,h,w){return a.isPlainObject(l)&&a.isPlainObject(u)?a.merge.call({caseless:w},l,u):a.isPlainObject(u)?a.merge({},u):a.isArray(u)?u.slice():u}function s(l,u,h,w){if(a.isUndefined(u)){if(!a.isUndefined(l))return n(void 0,l,h,w)}else return n(l,u,h,w)}function o(l,u){if(!a.isUndefined(u))return n(void 0,u)}function i(l,u){if(a.isUndefined(u)){if(!a.isUndefined(l))return n(void 0,l)}else return n(void 0,u)}function c(l,u,h){if(h in t)return n(l,u);if(h in e)return n(void 0,l)}const f={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(l,u,h)=>s(Ee(l),Ee(u),h,!0)};return a.forEach(Object.keys(Object.assign({},e,t)),function(u){const h=f[u]||s,w=h(e[u],t[u],u);a.isUndefined(w)&&h!==c||(r[u]=w)}),r}const Ve=e=>{const t=U({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:c}=t;t.headers=i=C.from(i),t.url=Ie(Je(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let f;if(a.isFormData(r)){if(R.hasStandardBrowserEnv||R.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((f=i.getContentType())!==!1){const[l,...u]=f?f.split(";").map(h=>h.trim()).filter(Boolean):[];i.setContentType([l||"multipart/form-data",...u].join("; "))}}if(R.hasStandardBrowserEnv&&(n&&a.isFunction(n)&&(n=n(t)),n||n!==!1&&ur(t.url))){const l=s&&o&&fr.read(o);l&&i.set(s,l)}return t},pr=typeof XMLHttpRequest<"u",mr=pr&&function(e){return new Promise(function(r,n){const s=Ve(e);let o=s.data;const i=C.from(s.headers).normalize();let{responseType:c,onUploadProgress:f,onDownloadProgress:l}=s,u,h,w,S,d;function y(){S&&S(),d&&d(),s.cancelToken&&s.cancelToken.unsubscribe(u),s.signal&&s.signal.removeEventListener("abort",u)}let p=new XMLHttpRequest;p.open(s.method.toUpperCase(),s.url,!0),p.timeout=s.timeout;function b(){if(!p)return;const A=C.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders()),O={data:!c||c==="text"||c==="json"?p.responseText:p.response,status:p.status,statusText:p.statusText,headers:A,config:e,request:p};$e(function(F){r(F),y()},function(F){n(F),y()},O),p=null}"onloadend"in p?p.onloadend=b:p.onreadystatechange=function(){!p||p.readyState!==4||p.status===0&&!(p.responseURL&&p.responseURL.indexOf("file:")===0)||setTimeout(b)},p.onabort=function(){p&&(n(new m("Request aborted",m.ECONNABORTED,e,p)),p=null)},p.onerror=function(){n(new m("Network Error",m.ERR_NETWORK,e,p)),p=null},p.ontimeout=function(){let P=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const O=s.transitional||He;s.timeoutErrorMessage&&(P=s.timeoutErrorMessage),n(new m(P,O.clarifyTimeoutError?m.ETIMEDOUT:m.ECONNABORTED,e,p)),p=null},o===void 0&&i.setContentType(null),"setRequestHeader"in p&&a.forEach(i.toJSON(),function(P,O){p.setRequestHeader(O,P)}),a.isUndefined(s.withCredentials)||(p.withCredentials=!!s.withCredentials),c&&c!=="json"&&(p.responseType=s.responseType),l&&([w,d]=W(l,!0),p.addEventListener("progress",w)),f&&p.upload&&([h,S]=W(f),p.upload.addEventListener("progress",h),p.upload.addEventListener("loadend",S)),(s.cancelToken||s.signal)&&(u=A=>{p&&(n(!A||A.type?new k(null,e,p):A),p.abort(),p=null)},s.cancelToken&&s.cancelToken.subscribe(u),s.signal&&(s.signal.aborted?u():s.signal.addEventListener("abort",u)));const E=ar(s.url);if(E&&R.protocols.indexOf(E)===-1){n(new m("Unsupported protocol "+E+":",m.ERR_BAD_REQUEST,e));return}p.send(o||null)})},yr=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let n=new AbortController,s;const o=function(l){if(!s){s=!0,c();const u=l instanceof Error?l:this.reason;n.abort(u instanceof m?u:new k(u instanceof Error?u.message:u))}};let i=t&&setTimeout(()=>{i=null,o(new m(`timeout ${t} of ms exceeded`,m.ETIMEDOUT))},t);const c=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(l=>{l.unsubscribe?l.unsubscribe(o):l.removeEventListener("abort",o)}),e=null)};e.forEach(l=>l.addEventListener("abort",o));const{signal:f}=n;return f.unsubscribe=()=>a.asap(c),f}},wr=function*(e,t){let r=e.byteLength;if(r<t){yield e;return}let n=0,s;for(;n<r;)s=n+t,yield e.slice(n,s),n=s},br=async function*(e,t){for await(const r of gr(e))yield*wr(r,t)},gr=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:n}=await t.read();if(r)break;yield n}}finally{await t.cancel()}},Ae=(e,t,r,n)=>{const s=br(e,t);let o=0,i,c=f=>{i||(i=!0,n&&n(f))};return new ReadableStream({async pull(f){try{const{done:l,value:u}=await s.next();if(l){c(),f.close();return}let h=u.byteLength;if(r){let w=o+=h;r(w)}f.enqueue(new Uint8Array(u))}catch(l){throw c(l),l}},cancel(f){return c(f),s.return()}},{highWaterMark:2})},Y=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",We=Y&&typeof ReadableStream=="function",Sr=Y&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Ke=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Er=We&&Ke(()=>{let e=!1;const t=new Request(R.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Re=64*1024,ae=We&&Ke(()=>a.isReadableStream(new Response("").body)),K={stream:ae&&(e=>e.body)};Y&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!K[t]&&(K[t]=a.isFunction(e[t])?r=>r[t]():(r,n)=>{throw new m(`Response type '${t}' is not supported`,m.ERR_NOT_SUPPORT,n)})})})(new Response);const Ar=async e=>{if(e==null)return 0;if(a.isBlob(e))return e.size;if(a.isSpecCompliantForm(e))return(await new Request(R.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(a.isArrayBufferView(e)||a.isArrayBuffer(e))return e.byteLength;if(a.isURLSearchParams(e)&&(e=e+""),a.isString(e))return(await Sr(e)).byteLength},Rr=async(e,t)=>{const r=a.toFiniteNumber(e.getContentLength());return r??Ar(t)},Or=Y&&(async e=>{let{url:t,method:r,data:n,signal:s,cancelToken:o,timeout:i,onDownloadProgress:c,onUploadProgress:f,responseType:l,headers:u,withCredentials:h="same-origin",fetchOptions:w}=Ve(e);l=l?(l+"").toLowerCase():"text";let S=yr([s,o&&o.toAbortSignal()],i),d;const y=S&&S.unsubscribe&&(()=>{S.unsubscribe()});let p;try{if(f&&Er&&r!=="get"&&r!=="head"&&(p=await Rr(u,n))!==0){let O=new Request(t,{method:"POST",body:n,duplex:"half"}),L;if(a.isFormData(n)&&(L=O.headers.get("content-type"))&&u.setContentType(L),O.body){const[F,z]=ge(p,W(Se(f)));n=Ae(O.body,Re,F,z)}}a.isString(h)||(h=h?"include":"omit");const b="credentials"in Request.prototype;d=new Request(t,{...w,signal:S,method:r.toUpperCase(),headers:u.normalize().toJSON(),body:n,duplex:"half",credentials:b?h:void 0});let E=await fetch(d,w);const A=ae&&(l==="stream"||l==="response");if(ae&&(c||A&&y)){const O={};["status","statusText","headers"].forEach(he=>{O[he]=E[he]});const L=a.toFiniteNumber(E.headers.get("content-length")),[F,z]=c&&ge(L,W(Se(c),!0))||[];E=new Response(Ae(E.body,Re,F,()=>{z&&z(),y&&y()}),O)}l=l||"text";let P=await K[a.findKey(K,l)||"text"](E,e);return!A&&y&&y(),await new Promise((O,L)=>{$e(O,L,{data:P,headers:C.from(E.headers),status:E.status,statusText:E.statusText,config:e,request:d})})}catch(b){throw y&&y(),b&&b.name==="TypeError"&&/Load failed|fetch/i.test(b.message)?Object.assign(new m("Network Error",m.ERR_NETWORK,e,d),{cause:b.cause||b}):m.from(b,b&&b.code,e,d)}}),ce={http:It,xhr:mr,fetch:Or};a.forEach(ce,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Oe=e=>`- ${e}`,Tr=e=>a.isFunction(e)||e===null||e===!1,ve={getAdapter:e=>{e=a.isArray(e)?e:[e];const{length:t}=e;let r,n;const s={};for(let o=0;o<t;o++){r=e[o];let i;if(n=r,!Tr(r)&&(n=ce[(i=String(r)).toLowerCase()],n===void 0))throw new m(`Unknown adapter '${i}'`);if(n)break;s[i||"#"+o]=n}if(!n){const o=Object.entries(s).map(([c,f])=>`adapter ${c} `+(f===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Oe).join(`
`):" "+Oe(o[0]):"as no adapter specified";throw new m("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return n},adapters:ce};function ne(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new k(null,e)}function Te(e){return ne(e),e.headers=C.from(e.headers),e.data=re.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ve.getAdapter(e.adapter||H.adapter)(e).then(function(n){return ne(e),n.data=re.call(e,e.transformResponse,n),n.headers=C.from(n.headers),n},function(n){return ze(n)||(ne(e),n&&n.response&&(n.response.data=re.call(e,e.transformResponse,n.response),n.response.headers=C.from(n.response.headers))),Promise.reject(n)})}const Qe="1.10.0",ee={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{ee[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const Ce={};ee.transitional=function(t,r,n){function s(o,i){return"[Axios v"+Qe+"] Transitional option '"+o+"'"+i+(n?". "+n:"")}return(o,i,c)=>{if(t===!1)throw new m(s(i," has been removed"+(r?" in "+r:"")),m.ERR_DEPRECATED);return r&&!Ce[i]&&(Ce[i]=!0,console.warn(s(i," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(o,i,c):!0}};ee.spelling=function(t){return(r,n)=>(console.warn(`${n} is likely a misspelling of ${t}`),!0)};function Cr(e,t,r){if(typeof e!="object")throw new m("options must be an object",m.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let s=n.length;for(;s-- >0;){const o=n[s],i=t[o];if(i){const c=e[o],f=c===void 0||i(c,o,e);if(f!==!0)throw new m("option "+o+" must be "+f,m.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new m("Unknown option "+o,m.ERR_BAD_OPTION)}}const V={assertOptions:Cr,validators:ee},N=V.validators;let _=class{constructor(t){this.defaults=t||{},this.interceptors={request:new we,response:new we}}async request(t,r){try{return await this._request(t,r)}catch(n){if(n instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{n.stack?o&&!String(n.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+o):n.stack=o}catch{}}throw n}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=U(this.defaults,r);const{transitional:n,paramsSerializer:s,headers:o}=r;n!==void 0&&V.assertOptions(n,{silentJSONParsing:N.transitional(N.boolean),forcedJSONParsing:N.transitional(N.boolean),clarifyTimeoutError:N.transitional(N.boolean)},!1),s!=null&&(a.isFunction(s)?r.paramsSerializer={serialize:s}:V.assertOptions(s,{encode:N.function,serialize:N.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),V.assertOptions(r,{baseUrl:N.spelling("baseURL"),withXsrfToken:N.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let i=o&&a.merge(o.common,o[r.method]);o&&a.forEach(["delete","get","head","post","put","patch","common"],d=>{delete o[d]}),r.headers=C.concat(i,o);const c=[];let f=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(r)===!1||(f=f&&y.synchronous,c.unshift(y.fulfilled,y.rejected))});const l=[];this.interceptors.response.forEach(function(y){l.push(y.fulfilled,y.rejected)});let u,h=0,w;if(!f){const d=[Te.bind(this),void 0];for(d.unshift.apply(d,c),d.push.apply(d,l),w=d.length,u=Promise.resolve(r);h<w;)u=u.then(d[h++],d[h++]);return u}w=c.length;let S=r;for(h=0;h<w;){const d=c[h++],y=c[h++];try{S=d(S)}catch(p){y.call(this,p);break}}try{u=Te.call(this,S)}catch(d){return Promise.reject(d)}for(h=0,w=l.length;h<w;)u=u.then(l[h++],l[h++]);return u}getUri(t){t=U(this.defaults,t);const r=Je(t.baseURL,t.url,t.allowAbsoluteUrls);return Ie(r,t.params,t.paramsSerializer)}};a.forEach(["delete","get","head","options"],function(t){_.prototype[t]=function(r,n){return this.request(U(n||{},{method:t,url:r,data:(n||{}).data}))}});a.forEach(["post","put","patch"],function(t){function r(n){return function(o,i,c){return this.request(U(c||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}_.prototype[t]=r(),_.prototype[t+"Form"]=r(!0)});let xr=class Xe{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(o){r=o});const n=this;this.promise.then(s=>{if(!n._listeners)return;let o=n._listeners.length;for(;o-- >0;)n._listeners[o](s);n._listeners=null}),this.promise.then=s=>{let o;const i=new Promise(c=>{n.subscribe(c),o=c}).then(s);return i.cancel=function(){n.unsubscribe(o)},i},t(function(o,i,c){n.reason||(n.reason=new k(o,i,c),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=n=>{t.abort(n)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new Xe(function(s){t=s}),cancel:t}}};function Nr(e){return function(r){return e.apply(null,r)}}function Pr(e){return a.isObject(e)&&e.isAxiosError===!0}const le={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(le).forEach(([e,t])=>{le[t]=e});function Ge(e){const t=new _(e),r=xe(_.prototype.request,t);return a.extend(r,_.prototype,t,{allOwnKeys:!0}),a.extend(r,t,null,{allOwnKeys:!0}),r.create=function(s){return Ge(U(e,s))},r}const g=Ge(H);g.Axios=_;g.CanceledError=k;g.CancelToken=xr;g.isCancel=ze;g.VERSION=Qe;g.toFormData=Z;g.AxiosError=m;g.Cancel=g.CanceledError;g.all=function(t){return Promise.all(t)};g.spread=Nr;g.isAxiosError=Pr;g.mergeConfig=U;g.AxiosHeaders=C;g.formToJSON=e=>Me(a.isHTMLForm(e)?new FormData(e):e);g.getAdapter=ve.getAdapter;g.HttpStatusCode=le;g.default=g;const{Axios:jr,AxiosError:qr,CanceledError:Ir,isCancel:Hr,CancelToken:Mr,VERSION:zr,all:$r,Cancel:Jr,isAxiosError:Vr,spread:Wr,toFormData:Kr,AxiosHeaders:vr,HttpStatusCode:Qr,formToJSON:Xr,getAdapter:Gr,mergeConfig:Zr}=g,Lr=()=>"https://your-domain.com/api",M=g.create({baseURL:Lr(),timeout:1e4,headers:{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:"0"}}),Ze=async(e={})=>{try{return(await M.get("/articles",{params:e})).data}catch(t){return console.error("加载文章失败:",t),{success:!1,error:t.message||"网络请求失败"}}},Fr=async e=>{try{return(await M.get(`/articles/${e}`)).data}catch(t){return console.error("加载文章详情失败:",t),{success:!1,error:t.message||"网络请求失败"}}},Dr=async()=>{try{return(await M.get("/stats")).data}catch(e){return console.error("获取统计数据失败:",e),{success:!1,error:e.message||"网络请求失败"}}},Yr=async(e,t)=>{try{return(await M.put(`/articles/${e}`,t)).data}catch(r){return console.error("保存文章到数据库失败:",r),{success:!1,error:r.message||"保存失败"}}},en=async e=>{try{return(await M.post("/articles",e)).data}catch(t){return console.error("创建文章失败:",t),{success:!1,error:t.message||"创建失败"}}},_r=async(e,t={})=>{try{const r=await Ze();if(!r.success)return r;let n=r.data;if(e){const s=e.toLowerCase();n=n.filter(o=>o.title.toLowerCase().includes(s)||o.topic.toLowerCase().includes(s)||o.summary.toLowerCase().includes(s))}return t.status&&(n=n.filter(s=>s.status===t.status)),t.category&&(n=n.filter(s=>s.category===t.category)),{success:!0,data:n,total:n.length}}catch(r){return{success:!1,error:r.message}}},tn=Ye("articles",{state:()=>({articles:[],currentArticle:null,currentDate:new Date().toISOString().split("T")[0],stats:{totalArticles:0,draftArticles:0,publishedArticles:0,todayViews:0,categories:{}},loading:!1,error:null,searchQuery:"",filters:{status:"",category:"",sortBy:"createdAt",sortOrder:"desc"},pagination:{currentPage:1,pageSize:20,total:0}}),getters:{filteredArticles:e=>{if(!e.articles||!Array.isArray(e.articles))return[];let t=[...e.articles];if(e.searchQuery){const r=e.searchQuery.toLowerCase();t=t.filter(n=>n.title.toLowerCase().includes(r)||n.topic.toLowerCase().includes(r)||n.summary.toLowerCase().includes(r)||n.tags.some(s=>s.toLowerCase().includes(r)))}return e.filters.status&&(t=t.filter(r=>r.status===e.filters.status)),e.filters.category&&(t=t.filter(r=>r.category===e.filters.category)),t.sort((r,n)=>{const s=r[e.filters.sortBy],o=n[e.filters.sortBy];return e.filters.sortOrder==="asc"?s>o?1:-1:s<o?1:-1}),t},recentArticles:e=>!e.articles||!Array.isArray(e.articles)?[]:e.articles.slice().sort((t,r)=>new Date(r.updatedAt)-new Date(t.updatedAt)).slice(0,5),articlesByCategory:e=>{if(!e.articles||!Array.isArray(e.articles))return{};const t={};return e.articles.forEach(r=>{t[r.category]||(t[r.category]=0),t[r.category]++}),t},articlesByStatus:e=>{if(!e.articles||!Array.isArray(e.articles))return{};const t={};return e.articles.forEach(r=>{t[r.status]||(t[r.status]=0),t[r.status]++}),t}},actions:{async loadArticles(e={}){this.loading=!0,this.error=null;try{console.log("[Store] 开始加载文章列表",e);const t=await Ze(e);t.success?(console.log(`[Store] 成功加载 ${t.data.length} 篇文章`),this.articles=t.data,this.pagination.total=t.total,t.currentDate&&(this.currentDate=t.currentDate),this.updateStats()):(console.error("[Store] 加载文章失败:",t.error),this.error=t.error)}catch(t){console.error("[Store] 加载文章异常:",t),this.error=t.message}finally{this.loading=!1}},async loadArticle(e){this.loading=!0,this.error=null;try{console.log(`[Store] 加载文章详情 ID: ${e}`);const t=await Fr(e);return t.success?(this.currentArticle=t.data,console.log("[Store] 文章详情加载成功"),t.data):(console.error("[Store] 文章详情加载失败:",t.error),this.error=t.error,null)}catch(t){return console.error("[Store] 文章详情加载异常:",t),this.error=t.message,null}finally{this.loading=!1}},async searchArticles(e,t={}){this.loading=!0,this.error=null;try{const r=await _r(e,t);r.success?(this.articles=r.data,this.pagination.total=r.total,this.searchQuery=e,this.filters={...this.filters,...t}):this.error=r.error}catch(r){this.error=r.message}finally{this.loading=!1}},async updateStats(){try{const e=await Dr();e.success?(this.stats={totalArticles:e.data.totalArticles,draftArticles:e.data.draftArticles,publishedArticles:e.data.publishedArticles,archivedArticles:e.data.archivedArticles,todayArticles:e.data.todayArticles,categories:this.articlesByCategory},console.log("[Store] 统计数据更新:",this.stats)):console.error("[Store] 获取统计数据失败:",e.error)}catch(e){console.error("[Store] 更新统计数据异常:",e)}},setSearchQuery(e){this.searchQuery=e},setFilters(e){this.filters={...this.filters,...e}},resetFilters(){this.searchQuery="",this.filters={status:"",category:"",sortBy:"createdAt",sortOrder:"desc"}},setPagination(e){this.pagination={...this.pagination,...e}},addArticle(e){this.articles.unshift({...e,id:Date.now(),createdAt:new Date().toLocaleString("zh-CN"),updatedAt:new Date().toLocaleString("zh-CN")}),this.updateStats()},updateArticle(e,t){const r=this.articles.findIndex(n=>n.id==e);return r>-1?(this.articles[r]={...this.articles[r],...t,updatedAt:new Date().toLocaleString("zh-CN")},this.currentArticle&&this.currentArticle.id==e&&(this.currentArticle={...this.currentArticle,...t,updatedAt:new Date().toLocaleString("zh-CN")}),this.updateStats(),!0):!1},deleteArticle(e){const t=this.articles.findIndex(r=>r.id===e);t>-1&&(this.articles.splice(t,1),this.updateStats())},batchUpdateStatus(e,t){e.forEach(r=>{const n=this.articles.findIndex(s=>s.id===r);n>-1&&(this.articles[n].status=t,this.articles[n].updatedAt=new Date().toLocaleString("zh-CN"))}),this.updateStats()},clearError(){this.error=null}}});export{en as c,Yr as s,tn as u};
