# 生产环境配置 - Docker部署

# 数据库配置 - 重要：Docker容器中不能使用localhost！
# 选择以下其中一种配置：

# 方案1: Linux服务器 - 使用Docker默认网桥IP
DB_HOST=**********
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=news_system

# 方案2: Windows/Mac Docker Desktop - 使用特殊主机名
# DB_HOST=host.docker.internal
# DB_PORT=3306
# DB_USER=root
# DB_PASSWORD=root
# DB_NAME=news_system

# 方案3: 使用服务器实际IP地址
# DB_HOST=*************  # 替换为您服务器的实际IP
# DB_PORT=3306
# DB_USER=root
# DB_PASSWORD=root
# DB_NAME=news_system

# 应用配置
PORT=9485
HOST=0.0.0.0
NODE_ENV=production

# 时区设置
TZ=Asia/Shanghai

# API配置
VITE_API_BASE_URL=http://localhost:9485/api

# 数据库配置（生产环境MySQL）
DB_HOST=your-production-db-host.com
DB_PORT=3306
DB_USER=your_db_user
DB_PASSWORD=your_secure_password
DB_NAME=smart_notes_db

# CORS配置（生产环境）
CORS_ORIGIN=https://your-domain.com

# 其他配置
LOG_LEVEL=warn
