# 生产环境配置 - Docker部署
# 服务器IP: http://**************/
# 数据库密码: Huangkun729.

# 数据库配置 - 使用host网络模式，可以直接连接localhost
# 注意：docker-compose.yml 已配置为 network_mode: "host"
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=Huangkun729.
DB_NAME=news_system

# 备用方案（如果不使用host网络模式）：
# 方案1: Linux服务器 - 使用Docker默认网桥IP
# DB_HOST=**********
# 方案2: Windows/Mac Docker Desktop - 使用特殊主机名
# DB_HOST=host.docker.internal
# 方案3: 使用服务器实际IP地址
# DB_HOST=**************

# 应用配置
PORT=9485
HOST=0.0.0.0
NODE_ENV=production

# 时区设置
TZ=Asia/Shanghai

# API配置 - 根据部署环境选择
# 本地开发/测试
# VITE_API_BASE_URL=http://localhost:9485/api

# 服务器部署 - 使用您的服务器IP (**************)
VITE_API_BASE_URL=http://**************:9485/api

# 生产环境 - 使用相对路径（推荐，如果前后端同域部署）
# VITE_API_BASE_URL=/api

# 数据库配置（生产环境MySQL）
DB_HOST=your-production-db-host.com
DB_PORT=3306
DB_USER=your_db_user
DB_PASSWORD=your_secure_password
DB_NAME=smart_notes_db

# CORS配置（生产环境）
CORS_ORIGIN=https://your-domain.com

# 其他配置
LOG_LEVEL=warn
