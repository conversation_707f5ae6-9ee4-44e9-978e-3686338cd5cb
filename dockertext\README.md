# 智能笔记网站

一个基于Vue 3 + Express.js的现代化文章管理系统，支持文章的创建、编辑、分类和状态管理。

## 功能特性

### 📝 文章管理
- **富文本编辑器**：基于Quill.js的强大编辑器
- **实时保存**：支持Ctrl+S快捷键保存
- **状态管理**：草稿、已发布、已归档状态
- **分类标签**：支持文章分类和标签管理
- **摘要生成**：自动生成文章摘要

### 🔍 智能筛选
- **日期筛选**：按创建日期查看文章
- **状态筛选**：按发布状态筛选
- **分类筛选**：按话题分类筛选
- **排序功能**：支持多种排序方式
- **搜索功能**：全文搜索文章内容

### 🎨 用户体验
- **响应式设计**：适配各种屏幕尺寸
- **黑白主题**：简洁的黑白对比设计
- **状态保持**：筛选条件URL持久化
- **流畅动画**：平滑的页面过渡效果
- **懒加载**：优化大量文章的加载性能

### 📊 数据统计
- **实时仪表板**：文章统计和趋势分析
- **状态分布**：各状态文章数量统计
- **时间分析**：按时间维度的数据展示

## 技术栈

### 前端
- **Vue 3** - 渐进式JavaScript框架
- **Element Plus** - Vue 3组件库
- **Vue Router** - 官方路由管理器
- **Pinia** - 状态管理
- **Quill.js** - 富文本编辑器
- **Vite** - 现代化构建工具

### 后端
- **Node.js** - JavaScript运行时
- **Express.js** - Web应用框架
- **MySQL** - 数据库
- **CORS** - 跨域资源共享

## 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖

```bash
# 安装前端依赖（包含后端服务器）
cd my-vue-app
npm install
```

### 启动开发服务器

```bash
# 启动后端数据库服务器 (端口: 9486)
cd my-vue-app
node database-server.js

# 启动前端开发服务器 (端口: 5173)
npm run dev
```

### 访问应用
- 前端应用：http://localhost:5173
- 后端API：http://localhost:9486
- 健康检查：http://localhost:9486/health

## 项目结构

```
├── my-vue-app/                 # 前端Vue应用
│   ├── src/
│   │   ├── components/         # 可复用组件
│   │   ├── views/             # 页面组件
│   │   ├── stores/            # Pinia状态管理
│   │   ├── api/               # API接口
│   │   └── assets/            # 静态资源
│   ├── public/                # 公共文件
│   ├── package.json           # 前端依赖配置
│   └── database-server.js     # 后端数据库服务器
├── .env                       # 环境变量配置
├── .env.example              # 环境变量模板
├── project_document/         # 项目文档
└── README.md                 # 项目说明
```

## API接口

### 文章相关
- `GET /api/articles` - 获取文章列表
- `GET /api/articles/:id` - 获取单篇文章
- `POST /api/articles` - 创建文章
- `PUT /api/articles/:id` - 更新文章
- `DELETE /api/articles/:id` - 删除文章

### 统计相关
- `GET /api/stats` - 获取统计数据

## 数据库设计

### articles表
- `id` - 主键
- `title` - 标题
- `content` - 内容
- `summary` - 摘要
- `status` - 状态 (draft/published/archived)
- `category` - 分类
- `tags` - 标签
- `created_at` - 创建时间
- `updated_at` - 更新时间

### 环境变量配置
项目使用环境变量进行配置管理，支持开发和生产环境：
- `.env` - 实际配置文件（不提交到Git）
- `.env.example` - 配置模板
- `.env.production` - 生产环境模板

## 开发指南

### 添加新功能
1. 在`my-vue-app/src/views/`中创建新页面
2. 在`my-vue-app/src/api/`中添加API接口
3. 在后端`server.js`中添加对应路由
4. 更新路由配置

### 样式规范
- 使用CSS变量定义主题色彩
- 遵循响应式设计原则
- 保持黑白对比的设计风格

## 部署

### Docker部署（推荐）

#### 1. 环境变量配置
```bash
# 复制并编辑环境变量
cp .env.example .env
cp my-vue-app/.env.example my-vue-app/.env

# 编辑配置文件
nano .env                    # 修改数据库、端口等配置
nano my-vue-app/.env        # 修改API地址
```

**后端 .env 配置示例：**
```env
NODE_ENV=production
PORT=9486
HOST=0.0.0.0
CORS_ORIGIN=https://your-domain.com
DB_HOST=your-db-host.com
DB_USER=your_db_user
DB_PASSWORD=your_secure_password
```

**前端 .env 配置示例：**
```env
# 使用相对路径（推荐，同域部署）
VITE_API_BASE_URL=/api

# 或使用域名
VITE_API_BASE_URL=https://your-domain.com/api
```

#### 2. 使用Docker Compose部署
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看运行状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart
```

#### 3. 访问应用
- **前端应用**: http://your-server-ip
- **后端API**: http://your-server-ip:9486
- **健康检查**: http://your-server-ip:9486/health

### 服务器要求
- **Docker**: >= 20.0
- **Docker Compose**: >= 2.0
- **内存**: >= 1GB
- **磁盘**: >= 2GB
- **端口**: 80, 443, 9486

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 更新日志

### v1.0.0 (2025-01-01)
- ✨ 初始版本发布
- 📝 完整的文章管理功能
- 🎨 响应式UI设计
- 📊 数据统计仪表板
- 🔍 智能筛选和搜索
#   d o c k e r t e x t 
 
 
