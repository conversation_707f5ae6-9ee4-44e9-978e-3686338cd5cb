#!/bin/bash

# 文章管理系统 Docker 自动部署脚本
# 版本: 1.0
# 端口: 9485

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未安装，请先安装 $1"
        exit 1
    fi
}

# 检查端口是否被占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 $port 已被占用"
        read -p "是否继续部署？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "部署已取消"
            exit 0
        fi
    fi
}

# 检查 Docker 服务状态
check_docker_service() {
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker 服务未运行，请启动 Docker 服务"
        log_info "Ubuntu/Debian: sudo systemctl start docker"
        log_info "CentOS/RHEL: sudo systemctl start docker"
        log_info "Windows/macOS: 启动 Docker Desktop"
        exit 1
    fi
}

# 创建环境配置文件
create_env_file() {
    if [ ! -f .env ]; then
        log_info "创建环境配置文件..."
        cat > .env << EOF
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=news_system

# 应用配置
NODE_ENV=production
PORT=9485
HOST=0.0.0.0

# 时区设置
TZ=Asia/Shanghai

# API配置
VITE_API_BASE_URL=http://localhost:9485/api
EOF
        log_success "环境配置文件已创建"
    else
        log_info "环境配置文件已存在"
    fi
}

# 创建数据目录
create_data_directory() {
    if [ ! -d "data" ]; then
        log_info "创建数据目录..."
        mkdir -p data
        log_success "数据目录已创建"
    fi
}

# 构建和启动服务
deploy_service() {
    log_info "开始构建 Docker 镜像..."
    
    # 停止现有服务
    if docker compose ps | grep -q "smart-notes"; then
        log_info "停止现有服务..."
        docker compose down
    fi
    
    # 构建镜像
    log_info "构建新镜像..."
    docker compose build --no-cache
    
    # 启动服务
    log_info "启动服务..."
    docker compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker compose ps | grep -q "Up"; then
        log_success "服务启动成功！"
        return 0
    else
        log_error "服务启动失败"
        return 1
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:9485 >/dev/null 2>&1; then
            log_success "健康检查通过！"
            return 0
        fi
        
        log_info "等待服务响应... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_warning "健康检查超时，但服务可能仍在启动中"
    return 1
}

# 显示部署信息
show_deployment_info() {
    echo
    echo "=================================="
    echo "🎉 部署完成！"
    echo "=================================="
    echo "📍 访问地址: http://localhost:9485"
    echo "🔧 API地址:  http://localhost:9485/api"
    echo "📊 服务状态: docker compose ps"
    echo "📋 查看日志: docker compose logs -f smart-notes"
    echo "🛑 停止服务: docker compose down"
    echo "=================================="
    echo
}

# 显示服务状态
show_service_status() {
    echo "当前服务状态："
    docker compose ps
    echo
    echo "最近日志："
    docker compose logs --tail=10 smart-notes
}

# 主函数
main() {
    echo "=================================="
    echo "🚀 文章管理系统 Docker 部署脚本"
    echo "📦 版本: 1.0"
    echo "🌐 端口: 9485"
    echo "=================================="
    echo
    
    # 检查系统要求
    log_info "检查系统要求..."
    check_command "docker"
    check_command "docker"
    check_docker_service
    check_port 9485
    
    # 进入项目目录
    if [ ! -f "docker-compose.yml" ]; then
        log_error "未找到 docker-compose.yml 文件，请确保在正确的目录中运行此脚本"
        exit 1
    fi
    
    # 创建必要的文件和目录
    create_env_file
    create_data_directory
    
    # 部署服务
    if deploy_service; then
        # 健康检查
        health_check
        
        # 显示部署信息
        show_deployment_info
        show_service_status
        
        log_success "部署完成！请访问 http://localhost:9485"
    else
        log_error "部署失败，请查看错误日志"
        echo
        echo "故障排除："
        echo "1. 查看详细日志: docker compose logs smart-notes"
        echo "2. 检查端口占用: lsof -i :9485"
        echo "3. 检查配置文件: docker compose config"
        echo "4. 重新构建: docker compose build --no-cache"
        exit 1
    fi
}

# 处理命令行参数
case "${1:-}" in
    "status")
        show_service_status
        ;;
    "logs")
        docker compose logs -f smart-notes
        ;;
    "stop")
        log_info "停止服务..."
        docker compose down
        log_success "服务已停止"
        ;;
    "restart")
        log_info "重启服务..."
        docker compose restart
        log_success "服务已重启"
        ;;
    "update")
        log_info "更新部署..."
        docker compose down
        docker compose build --no-cache
        docker compose up -d
        log_success "更新完成"
        ;;
    "clean")
        log_info "清理 Docker 资源..."
        docker compose down
        docker system prune -f
        log_success "清理完成"
        ;;
    "help"|"-h"|"--help")
        echo "用法: $0 [命令]"
        echo
        echo "命令:"
        echo "  (无参数)  执行完整部署"
        echo "  status    显示服务状态"
        echo "  logs      查看实时日志"
        echo "  stop      停止服务"
        echo "  restart   重启服务"
        echo "  update    更新部署"
        echo "  clean     清理 Docker 资源"
        echo "  help      显示此帮助信息"
        ;;
    "")
        main
        ;;
    *)
        log_error "未知命令: $1"
        echo "使用 '$0 help' 查看可用命令"
        exit 1
        ;;
esac
