import{_ as ve,c as D,v as w,x as E,o as _e,a as i,b as n,e as o,d as O,f as a,w as s,g as r,u,h as pe,y as L,r as d,z as me,A as fe,F as x,m as z,B as R,t as g,C as ye,q as T,k as ge,D as be,G as ke,H as he,I as we,i as Ce,E as $,J as Se}from"./index-BSyp31Kj.js";import{u as Ve}from"./articles-CM6hyJvV.js";const xe={class:"article-list"},ze={class:"page-header"},Ae={class:"header-actions"},Be={class:"filters-section"},De={class:"search-bar"},Oe={class:"filters"},$e={class:"articles-container"},Ne={key:0,class:"loading-container"},Ye={key:1,class:"empty-container"},Me={key:2,class:"articles-grid"},Qe={class:"card-header"},Te={class:"article-status"},qe={class:"article-category"},Fe=["onClick"],Ie={class:"article-title"},Ue={class:"article-summary"},Ee={key:0,class:"article-keywords"},Le={class:"card-footer"},Re={class:"article-meta"},Je={class:"meta-item"},Pe={class:"meta-item"},je={class:"meta-item"},Ge={key:0,class:"pagination-container"},He={__name:"ArticleList",setup(Ke){const C=pe(),v=Ve(),N=D(()=>v.articles),J=D(()=>v.loading),q=D(()=>v.pagination.total),A=D({get:()=>v.searchQuery,set:t=>{v.setSearchQuery(t),_.value=1}}),b=w(new Date().toISOString().split("T")[0]),p=w(""),y=w(""),m=w("createdAt"),c=w("desc"),P=[{label:"全部状态",value:""},{label:"草稿",value:"draft"},{label:"已发布",value:"published"},{label:"已归档",value:"archived"}],j=[{label:"全部分类",value:""},{label:"时政",value:"时政"},{label:"娱乐",value:"娱乐"},{label:"科技",value:"科技"},{label:"体育",value:"体育"},{label:"社会",value:"社会"},{label:"国际",value:"国际"},{label:"财经",value:"财经"},{label:"其他",value:"其他"}],G=[{label:"创建时间",value:"createdAt"},{label:"更新时间",value:"updatedAt"},{label:"标题",value:"title"},{label:"浏览量",value:"views"}],_=w(1),S=w(20),V=async()=>{const t={page:_.value,limit:S.value,date:b.value,status:p.value,topic:A.value,sortBy:m.value,sortOrder:c.value};await v.loadArticles(t)},F=t=>{const e=C.currentRoute.value.query;C.push({path:`/article/edit/${t.id}`,query:{returnQuery:JSON.stringify(e)}})},H=async t=>{try{await Se.confirm(`确定要删除文章"${t.title}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await v.deleteArticle(t.id),$.success("删除成功")}catch(e){e!=="cancel"&&$.error(e.message||"删除失败")}},I=async(t,e)=>{try{v.updateArticle(t.id,{status:e}),$.success("状态更新成功")}catch{$.error("状态更新失败")}},K=t=>({draft:"warning",published:"success",archived:"info"})[t]||"default",W=t=>({draft:"草稿",published:"已发布",archived:"已归档"})[t]||t,X=t=>{if(!t)return"未知时间";try{const e=new Date(t);return isNaN(e.getTime())?"无效日期":e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch{return"日期错误"}},Z=t=>{if(t.summary&&t.summary.length>20)return t.summary.length>200?t.summary.substring(0,200)+"...":t.summary;if(t.content){const e=t.content.replace(/<[^>]*>/g,"").replace(/[#*`_\[\]]/g,"").replace(/\s+/g," ").trim();if(e.length>20)return e.length>200?e.substring(0,200)+"...":e}return t.title?`${t.title}...`:"暂无摘要"},ee=t=>{_.value=t,Y(),V()},te=t=>{S.value=t,_.value=1,Y(),V()},le=()=>{v.resetFilters(),b.value=new Date().toISOString().split("T")[0],p.value="",y.value="",m.value="createdAt",c.value="desc",_.value=1,V()};E([b,p,y,m,c],()=>{_.value=1,Y(),V(),v.setFilters({status:p.value,category:y.value,sortBy:m.value,sortOrder:c.value})}),E(A,()=>{_.value=1,V()});const ae=()=>{const t=C.currentRoute.value.query;t.date&&(b.value=t.date),t.status&&(p.value=t.status),t.category&&(y.value=t.category),t.sortBy&&(m.value=t.sortBy),t.sortOrder&&(c.value=t.sortOrder),t.page&&(_.value=parseInt(t.page)),t.pageSize&&(S.value=parseInt(t.pageSize))},Y=()=>{const t={date:b.value,page:_.value,pageSize:S.value};p.value&&(t.status=p.value),y.value&&(t.category=y.value),m.value!=="createdAt"&&(t.sortBy=m.value),c.value!=="desc"&&(t.sortOrder=c.value),C.replace({query:t})};return _e(()=>{ae(),V()}),(t,e)=>{const k=d("el-button"),se=d("el-input"),oe=d("el-date-picker"),M=d("el-option"),Q=d("el-select"),f=d("el-icon"),ne=d("el-button-group"),ue=d("el-skeleton"),re=d("el-empty"),U=d("el-tag"),B=d("el-dropdown-item"),de=d("el-dropdown-menu"),ie=d("el-dropdown"),ce=d("el-pagination");return n(),i("div",xe,[o("div",ze,[e[11]||(e[11]=o("div",{class:"header-content"},[o("h1",{class:"page-title"},"文章管理"),o("p",{class:"page-subtitle"},"管理您的所有文章内容")],-1)),o("div",Ae,[a(k,{type:"primary",icon:u(L),onClick:e[0]||(e[0]=l=>u(C).push("/article/edit"))},{default:s(()=>e[9]||(e[9]=[r(" 创建文章 ")])),_:1,__:[9]},8,["icon"]),a(k,{icon:u(me),onClick:e[1]||(e[1]=l=>u(C).push("/import"))},{default:s(()=>e[10]||(e[10]=[r(" 导入文件 ")])),_:1,__:[10]},8,["icon"])])]),o("div",Be,[o("div",De,[a(se,{modelValue:A.value,"onUpdate:modelValue":e[2]||(e[2]=l=>A.value=l),placeholder:"搜索文章标题或内容...","prefix-icon":u(fe),size:"large",clearable:""},null,8,["modelValue","prefix-icon"])]),o("div",Oe,[a(oe,{modelValue:b.value,"onUpdate:modelValue":e[3]||(e[3]=l=>b.value=l),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",size:"default",style:{width:"160px"}},null,8,["modelValue"]),a(Q,{modelValue:p.value,"onUpdate:modelValue":e[4]||(e[4]=l=>p.value=l),placeholder:"状态筛选",clearable:""},{default:s(()=>[(n(),i(x,null,z(P,l=>a(M,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),a(Q,{modelValue:y.value,"onUpdate:modelValue":e[5]||(e[5]=l=>y.value=l),placeholder:"分类筛选",clearable:""},{default:s(()=>[(n(),i(x,null,z(j,l=>a(M,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),a(Q,{modelValue:m.value,"onUpdate:modelValue":e[6]||(e[6]=l=>m.value=l),placeholder:"排序方式"},{default:s(()=>[(n(),i(x,null,z(G,l=>a(M,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),a(ne,null,{default:s(()=>[a(k,{type:c.value==="asc"?"primary":"default",onClick:e[7]||(e[7]=l=>c.value="asc")},{default:s(()=>[a(f,null,{default:s(()=>[a(u(R))]),_:1}),e[12]||(e[12]=r(" 升序 "))]),_:1,__:[12]},8,["type"]),a(k,{type:c.value==="desc"?"primary":"default",onClick:e[8]||(e[8]=l=>c.value="desc")},{default:s(()=>[a(f,null,{default:s(()=>[a(u(R))]),_:1}),e[13]||(e[13]=r(" 降序 "))]),_:1,__:[13]},8,["type"])]),_:1}),a(k,{onClick:le},{default:s(()=>e[14]||(e[14]=[r("重置")])),_:1,__:[14]})])]),o("div",$e,[J.value?(n(),i("div",Ne,[a(ue,{rows:5,animated:""})])):!N.value||N.value.length===0?(n(),i("div",Ye,[a(re,{description:"暂无文章数据"})])):(n(),i("div",Me,[(n(!0),i(x,null,z(N.value,l=>(n(),i("div",{key:l.id,class:"article-card"},[o("div",Qe,[o("div",Te,[a(U,{type:K(l.status),size:"small"},{default:s(()=>[r(g(W(l.status)),1)]),_:2},1032,["type"]),o("span",qe,g(l.category||"未分类"),1)]),a(ie,{trigger:"click"},{dropdown:s(()=>[a(de,null,{default:s(()=>[a(B,{onClick:h=>F(l)},{default:s(()=>[a(f,null,{default:s(()=>[a(u(L))]),_:1}),e[15]||(e[15]=r(" 编辑 "))]),_:2,__:[15]},1032,["onClick"]),l.status==="draft"?(n(),T(B,{key:0,onClick:h=>I(l,"published")},{default:s(()=>[a(f,null,{default:s(()=>[a(u(ge))]),_:1}),e[16]||(e[16]=r(" 发布 "))]),_:2,__:[16]},1032,["onClick"])):O("",!0),l.status==="published"?(n(),T(B,{key:1,onClick:h=>I(l,"archived")},{default:s(()=>[a(f,null,{default:s(()=>[a(u(be))]),_:1}),e[17]||(e[17]=r(" 归档 "))]),_:2,__:[17]},1032,["onClick"])):O("",!0),a(B,{divided:"",onClick:h=>H(l)},{default:s(()=>[a(f,null,{default:s(()=>[a(u(ke))]),_:1}),e[18]||(e[18]=r(" 删除 "))]),_:2,__:[18]},1032,["onClick"])]),_:2},1024)]),default:s(()=>[a(k,{text:"",icon:u(ye)},null,8,["icon"])]),_:2},1024)]),o("div",{class:"card-content",onClick:h=>F(l)},[o("h3",Ie,g(l.title),1),o("p",Ue,g(Z(l)),1),l.keywords&&l.keywords.length>0?(n(),i("div",Ee,[(n(!0),i(x,null,z((Array.isArray(l.keywords)?l.keywords:[]).slice(0,3),h=>(n(),T(U,{key:h,size:"small",type:"info",class:"keyword-tag"},{default:s(()=>[r(g(h),1)]),_:2},1024))),128))])):O("",!0)],8,Fe),o("div",Le,[o("div",Re,[o("span",Je,[a(f,null,{default:s(()=>[a(u(he))]),_:1}),r(" "+g(X(l.created_at||l.createdAt)),1)]),o("span",Pe,[a(f,null,{default:s(()=>[a(u(we))]),_:1}),r(" "+g(l.views)+" 次浏览 ",1)]),o("span",je,[a(f,null,{default:s(()=>[a(u(Ce))]),_:1}),r(" "+g(l.word_count||l.wordCount||0)+" 字 ",1)])])])]))),128))]))]),q.value>S.value?(n(),i("div",Ge,[a(ce,{"current-page":_.value,"page-size":S.value,total:q.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",background:"",onCurrentChange:ee,onSizeChange:te},null,8,["current-page","page-size","total"])])):O("",!0)])}}},Ze=ve(He,[["__scopeId","data-v-f67d7e22"]]);export{Ze as default};
