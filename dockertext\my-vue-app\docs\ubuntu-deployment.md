# Ubuntu 服务器部署教程

本教程将指导您在 Ubuntu 20.04/22.04 LTS 服务器上部署智能文章管理系统。

## 📋 部署概览

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx         │    │   Node.js       │    │   MySQL         │
│   (反向代理)     │────│   (Express.js)  │────│   (数据库)       │
│   Port: 80/443  │    │   Port: 9486    │    │   Port: 3306    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 部署环境
- **操作系统**: Ubuntu 20.04/22.04 LTS
- **Web服务器**: Nginx
- **应用服务器**: Node.js + Express.js
- **数据库**: MySQL 8.0+
- **进程管理**: PM2

## 🔧 环境准备

### 1. 更新系统
```bash
sudo apt update && sudo apt upgrade -y
```

### 2. 安装基础工具
```bash
sudo apt install -y curl wget git unzip software-properties-common
```

### 3. 创建应用用户
```bash
# 创建专用用户
sudo adduser --system --group --home /opt/article-system article-system

# 切换到应用目录
sudo mkdir -p /opt/article-system
sudo chown article-system:article-system /opt/article-system
```

## 📦 安装依赖

### 1. 安装 Node.js
```bash
# 安装 NodeSource 仓库
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -

# 安装 Node.js
sudo apt install -y nodejs

# 验证安装
node --version  # 应显示 v18.x.x
npm --version   # 应显示 9.x.x
```

### 2. 安装 MySQL
```bash
# 安装 MySQL 服务器
sudo apt install -y mysql-server

# 启动 MySQL 服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

### 3. 安装 Nginx
```bash
# 安装 Nginx
sudo apt install -y nginx

# 启动 Nginx 服务
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 4. 安装 PM2
```bash
# 全局安装 PM2
sudo npm install -g pm2

# 设置 PM2 开机启动
sudo pm2 startup systemd -u article-system --hp /opt/article-system
```

## 🗄️ 数据库配置

### 1. 创建数据库和用户
```bash
# 登录 MySQL
sudo mysql -u root -p

# 在 MySQL 中执行以下命令
```

```sql
-- 创建数据库
CREATE DATABASE news_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'article_user'@'localhost' IDENTIFIED BY 'your_secure_password';

-- 授权
GRANT ALL PRIVILEGES ON news_system.* TO 'article_user'@'localhost';
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

### 2. 创建数据表
```sql
USE news_system;

CREATE TABLE articles (
  id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  content LONGTEXT,
  summary TEXT,
  category VARCHAR(50),
  tags JSON,
  keywords JSON,
  source_urls JSON,
  word_count INT DEFAULT 0,
  quality_score DECIMAL(3,2) DEFAULT 0,
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  generation_method VARCHAR(50) DEFAULT 'manual',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  published_at TIMESTAMP NULL,
  INDEX idx_status (status),
  INDEX idx_category (category),
  INDEX idx_created_at (created_at)
);
```

## 📁 应用部署

### 1. 下载应用代码
```bash
# 切换到应用用户
sudo su - article-system

# 克隆代码（或上传代码包）
cd /opt/article-system
git clone <your-repository-url> .

# 或者上传代码包并解压
# wget <your-code-package-url>
# unzip <package-name>.zip
```

### 2. 安装依赖
```bash
# 安装 Node.js 依赖
npm install --production

# 构建前端
npm run build
```

### 3. 配置环境变量
```bash
# 创建生产环境配置
cat > .env << EOF
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=article_user
DB_PASSWORD=your_secure_password
DB_NAME=news_system

# 服务器配置
PORT=9486
NODE_ENV=production

# 安全配置
SESSION_SECRET=your_session_secret_key
JWT_SECRET=your_jwt_secret_key
EOF

# 设置文件权限
chmod 600 .env
```

### 4. 启动应用
```bash
# 使用 PM2 启动应用
pm2 start database-server.js --name "article-system"

# 保存 PM2 配置
pm2 save

# 查看应用状态
pm2 status
```

## 🌐 Nginx 配置

### 1. 创建 Nginx 配置文件
```bash
sudo nano /etc/nginx/sites-available/article-system
```

### 2. 添加配置内容
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 静态文件服务
    location / {
        root /opt/article-system/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API 代理
    location /api/ {
        proxy_pass http://localhost:9486;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # 日志
    access_log /var/log/nginx/article-system.access.log;
    error_log /var/log/nginx/article-system.error.log;
}
```

### 3. 启用站点
```bash
# 创建软链接
sudo ln -s /etc/nginx/sites-available/article-system /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启 Nginx
sudo systemctl restart nginx
```

## 🔒 SSL 证书配置

### 1. 安装 Certbot
```bash
sudo apt install -y certbot python3-certbot-nginx
```

### 2. 获取 SSL 证书
```bash
# 获取证书并自动配置 Nginx
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔥 防火墙配置

### 1. 配置 UFW
```bash
# 启用 UFW
sudo ufw enable

# 允许 SSH
sudo ufw allow ssh

# 允许 HTTP 和 HTTPS
sudo ufw allow 'Nginx Full'

# 查看状态
sudo ufw status
```

## 📊 监控和日志

### 1. PM2 监控
```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs article-system

# 查看监控面板
pm2 monit
```

### 2. 系统监控
```bash
# 安装 htop
sudo apt install -y htop

# 查看系统资源
htop

# 查看磁盘使用
df -h

# 查看内存使用
free -h
```

### 3. 日志管理
```bash
# 查看 Nginx 日志
sudo tail -f /var/log/nginx/article-system.access.log
sudo tail -f /var/log/nginx/article-system.error.log

# 查看 MySQL 日志
sudo tail -f /var/log/mysql/error.log

# 设置日志轮转
sudo nano /etc/logrotate.d/article-system
```

## 🚀 性能优化

### 1. Node.js 优化
```bash
# 在 PM2 配置中设置
pm2 start database-server.js --name "article-system" -i max --max-memory-restart 500M
```

### 2. MySQL 优化
```bash
# 编辑 MySQL 配置
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf

# 添加优化配置
[mysqld]
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
max_connections = 200
query_cache_size = 64M
```

### 3. Nginx 优化
```bash
# 编辑 Nginx 配置
sudo nano /etc/nginx/nginx.conf

# 优化配置
worker_processes auto;
worker_connections 1024;
keepalive_timeout 65;
gzip on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
```

## 🔧 常用命令

### 应用管理
```bash
# 重启应用
pm2 restart article-system

# 停止应用
pm2 stop article-system

# 查看日志
pm2 logs article-system --lines 100

# 重新加载应用
pm2 reload article-system
```

### 服务管理
```bash
# 重启 Nginx
sudo systemctl restart nginx

# 重启 MySQL
sudo systemctl restart mysql

# 查看服务状态
sudo systemctl status nginx
sudo systemctl status mysql
```

### 备份和恢复
```bash
# 数据库备份
mysqldump -u article_user -p news_system > backup_$(date +%Y%m%d_%H%M%S).sql

# 数据库恢复
mysql -u article_user -p news_system < backup_file.sql

# 应用代码备份
tar -czf app_backup_$(date +%Y%m%d_%H%M%S).tar.gz /opt/article-system
```

## ❗ 常见问题排查

### 1. 应用无法启动
```bash
# 检查端口占用
sudo netstat -tlnp | grep :9486

# 检查应用日志
pm2 logs article-system

# 检查环境变量
cat /opt/article-system/.env
```

### 2. 数据库连接失败
```bash
# 测试数据库连接
mysql -u article_user -p -h localhost news_system

# 检查 MySQL 状态
sudo systemctl status mysql

# 查看 MySQL 错误日志
sudo tail -f /var/log/mysql/error.log
```

### 3. Nginx 502 错误
```bash
# 检查后端服务
curl http://localhost:9486/api/stats

# 检查 Nginx 配置
sudo nginx -t

# 查看 Nginx 错误日志
sudo tail -f /var/log/nginx/error.log
```

### 4. 性能问题
```bash
# 查看系统负载
top
htop

# 查看内存使用
free -h

# 查看磁盘 I/O
iotop

# 查看网络连接
ss -tuln
```

## 📈 扩展部署

### 负载均衡配置
如需处理更高并发，可配置多实例负载均衡：

```bash
# 启动多个应用实例
pm2 start database-server.js --name "article-system" -i 4

# Nginx 负载均衡配置
upstream article_backend {
    server localhost:9486;
    server localhost:9487;
    server localhost:9488;
    server localhost:9489;
}
```

### 数据库主从配置
对于高可用需求，可配置 MySQL 主从复制。

### Redis 缓存
添加 Redis 缓存层提升性能：

```bash
# 安装 Redis
sudo apt install -y redis-server

# 配置 Redis
sudo nano /etc/redis/redis.conf
```

---

**部署完成后，您的文章管理系统将在 `http://your-domain.com` 上运行！**

如有问题，请查看相关日志文件或联系技术支持。
