# 🚀 智能笔记网站 - Docker Compose 完整部署教程

## 📋 **项目概述**

### 技术架构
- **前端**: Vue 3 + Vite + Element Plus + Vue Router
- **后端**: Node.js + Express + MySQL
- **部署**: Docker + Docker Compose
- **数据库**: 远程MySQL (Aiven云服务)

### 核心功能
- ✅ 文章管理系统（增删改查）
- ✅ 富文本编辑器
- ✅ 实时数据统计
- ✅ 智能搜索和筛选
- ✅ 响应式设计
- ✅ SPA路由支持

---

## 🛠️ **部署前准备**

### 1. 服务器要求
```bash
# 系统要求
- 操作系统: Linux (Ubuntu 20.04+ 推荐)
- 内存: 至少 2GB RAM
- 磁盘: 至少 5GB 可用空间
- 网络: 需要开放 9486 端口
```

### 2. 安装 Docker 环境
```bash
# 更新系统包
sudo apt update

# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

---

## 📁 **项目文件结构**

```
smart-notes-website/
├── Dockerfile                    # Docker镜像配置
├── docker-compose.yml           # Docker编排配置
├── .env.example                 # 后端环境变量模板
├── .env                         # 后端环境变量（需创建）
├── .dockerignore               # Docker忽略文件
├── my-vue-app/                 # 前端应用目录
│   ├── package.json            # 前端依赖配置
│   ├── database-server.js      # 后端服务器
│   ├── .env.example           # 前端环境变量模板
│   ├── .env                   # 前端环境变量（需创建）
│   ├── src/                   # 前端源代码
│   └── dist/                  # 前端构建输出（自动生成）
└── 生成文章_20250629_174526/   # 文章数据目录
```

---

## ⚙️ **配置环境变量**

### 1. 后端配置 (根目录 `.env`)
```bash
# 复制模板
cp .env.example .env

# 编辑配置
nano .env
```

```env
# 服务器配置
PORT=9486
HOST=0.0.0.0
NODE_ENV=production

# 文章文件夹路径
ARTICLES_FOLDER=./生成文章_20250629_174526

# 数据库配置（MySQL）
DB_HOST=mysql-24e746d3-kbxy2365806687-c9eb.b.aivencloud.com
DB_PORT=13304
DB_USER=avnadmin
DB_PASSWORD=AVNS_kX8_YlNEfE3RquyPaG6
DB_NAME=defaultdb

# CORS配置（改为您的服务器IP）
CORS_ORIGIN=http://YOUR_SERVER_IP:9486

# 其他配置
LOG_LEVEL=warn
```

### 2. 前端配置 (`my-vue-app/.env`)
```bash
# 复制模板
cp my-vue-app/.env.example my-vue-app/.env

# 编辑配置
nano my-vue-app/.env
```

```env
# API配置（改为您的服务器IP）
VITE_API_BASE_URL=http://YOUR_SERVER_IP:9486/api

# 应用配置
VITE_APP_TITLE=智能笔记网站
VITE_APP_VERSION=1.0.0

# 其他配置
VITE_ENABLE_MOCK=false
```

**⚠️ 重要：将 `YOUR_SERVER_IP` 替换为您的实际服务器IP地址！**

---

## 🔥 **防火墙配置**

```bash
# Ubuntu/Debian 系统
sudo ufw allow 9486
sudo ufw reload
sudo ufw status

# CentOS/RHEL 系统
sudo firewall-cmd --add-port=9486/tcp --permanent
sudo firewall-cmd --reload

# 验证端口开放
sudo netstat -tlnp | grep 9486
```

---

## 🐳 **Docker 配置文件**

### 1. Dockerfile
```dockerfile
# 智能笔记网站 Docker 配置
FROM node:20-alpine

# 设置工作目录
WORKDIR /app

# 复制前端package.json文件
COPY my-vue-app/package*.json ./

# 安装依赖
RUN npm install

# 复制前端代码
COPY my-vue-app/ ./

# 构建前端
RUN npm run build

# 创建数据目录
RUN mkdir -p /app/data

# 暴露端口
EXPOSE 9486

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=9486
ENV HOST=0.0.0.0

# 启动应用
CMD ["node", "database-server.js"]
```

### 2. docker-compose.yml
```yaml
services:
  smart-notes:
    build: .
    ports:
      - "9486:9486"
    environment:
      - NODE_ENV=production
      - PORT=9486
      - HOST=0.0.0.0
    env_file:
      - .env
    volumes:
      - ./data:/app/data
      - ./生成文章_20250629_174526:/app/生成文章_20250629_174526
    restart: unless-stopped
```

### 3. .dockerignore
```
node_modules
npm-debug.log
.env
.env.local
.env.production
.git
.gitignore
README.md
Dockerfile
docker-compose.yml
*.md
project_document
生成文章_*
data
logs
*.db
*.sqlite
```

---

## 🚀 **部署步骤**

### 1. 获取项目代码
```bash
# Git克隆（推荐）
git clone https://github.com/KyrieOkun/-231.git
cd -231

# 或手动上传项目文件到服务器
```

### 2. 配置环境变量
```bash
# 配置后端环境变量
cp .env.example .env
nano .env  # 修改CORS_ORIGIN为您的服务器IP

# 配置前端环境变量
cp my-vue-app/.env.example my-vue-app/.env
nano my-vue-app/.env  # 修改VITE_API_BASE_URL为您的服务器IP
```

### 3. 开放防火墙端口
```bash
sudo ufw allow 9486
```

### 4. 构建并启动服务
```bash
# 第一次部署
docker-compose up -d --build

# 查看启动状态
docker-compose ps

# 查看日志
docker-compose logs -f smart-notes
```

### 5. 验证部署成功
```bash
# 检查容器状态
docker-compose ps

# 测试健康检查
curl http://localhost:9486/health

# 测试API接口
curl http://localhost:9486/api/articles
```

---

## 🌐 **访问应用**

### 访问地址
- **前端应用**: `http://YOUR_SERVER_IP:9486`
- **API接口**: `http://YOUR_SERVER_IP:9486/api/articles`
- **健康检查**: `http://YOUR_SERVER_IP:9486/health`

### 功能验证
- ✅ 能看到文章列表和统计数据
- ✅ 能搜索和筛选文章
- ✅ 能查看文章详情
- ✅ 能编辑文章内容
- ✅ 页面刷新正常工作

---

## 🔧 **常用管理命令**

### 服务管理
```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 重新构建并启动
docker-compose up -d --build

# 查看服务状态
docker-compose ps
```

### 日志查看
```bash
# 查看实时日志
docker-compose logs -f smart-notes

# 查看最近100行日志
docker-compose logs --tail=100 smart-notes

# 查看资源使用
docker stats
```

### 数据备份
```bash
# 备份数据
tar -czf backup-$(date +%Y%m%d-%H%M%S).tar.gz ./data ./生成文章_20250629_174526

# 清理Docker资源
docker system prune -f
```

---

## 🚨 **故障排除**

### 常见问题及解决方案

#### 1. 容器启动失败
```bash
# 查看详细错误
docker-compose logs smart-notes

# 常见原因：
# - 端口被占用：sudo lsof -i :9486
# - 权限问题：sudo chown -R $USER:$USER ./
# - 内存不足：free -h
```

#### 2. 页面刷新出现404
```bash
# 检查SPA路由配置
# 确保database-server.js中有正确的中间件配置
```

#### 3. API请求失败
```bash
# 检查CORS配置
cat .env | grep CORS_ORIGIN

# 检查前端API地址
cat my-vue-app/.env | grep VITE_API_BASE_URL
```

#### 4. 数据库连接失败
```bash
# 检查数据库配置
cat .env | grep DB_

# 测试数据库连接
mysql -h mysql-24e746d3-kbxy2365806687-c9eb.b.aivencloud.com -P 13304 -u avnadmin -p defaultdb
```

---

## 💡 **核心技术要点**

### 1. SPA路由处理
```javascript
// 关键中间件：处理前端路由刷新问题
app.use((req, res, next) => {
  if (req.path.startsWith('/api/') || 
      req.path === '/health' || 
      req.path.includes('.') ||
      req.method !== 'GET') {
    return next()
  }
  res.sendFile(path.join(__dirname, 'dist', 'index.html'))
})
```

### 2. 静态文件服务
```javascript
// 服务前端构建文件
app.use(express.static(path.join(__dirname, 'dist')))
```

### 3. 数据库连接池
```javascript
// MySQL连接池配置
const pool = mysql.createPool({
  ...DB_CONFIG,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
})
```

---

## ✅ **部署成功检查清单**

- [ ] Docker 和 Docker Compose 已安装
- [ ] 项目文件已上传到服务器
- [ ] 两个 .env 文件已正确配置
- [ ] 防火墙端口 9486 已开放
- [ ] 容器状态显示 "Up"
- [ ] 健康检查返回正常
- [ ] 前端页面可以正常访问
- [ ] API 接口响应正常
- [ ] 页面刷新功能正常
- [ ] 数据库连接成功

---

## 🎯 **部署完成**

恭喜！您的智能笔记网站已经成功部署！

**访问地址**: `http://YOUR_SERVER_IP:9486`

**管理命令**: `docker-compose logs -f smart-notes`

---

## 📞 **技术支持**

如遇到问题，请：
1. 查看容器日志：`docker-compose logs -f smart-notes`
2. 检查环境变量配置
3. 验证防火墙端口开放
4. 确认服务器资源充足

**项目仓库**: https://github.com/KyrieOkun/-231
