<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Sort, Edit, Delete, View, Search, Upload, Clock, Document, MoreFilled, Check, Box } from '@element-plus/icons-vue'
import { useArticlesStore } from '../../stores/articles.js'

const router = useRouter()
const articlesStore = useArticlesStore()

// 计算属性
const articles = computed(() => articlesStore.articles) // 直接使用API返回的分页数据
const loading = computed(() => articlesStore.loading)
const total = computed(() => articlesStore.pagination.total) // 使用API返回的总数
const searchQuery = computed({
  get: () => articlesStore.searchQuery,
  set: (value) => {
    articlesStore.setSearchQuery(value)
    currentPage.value = 1 // 搜索时重置到第一页
  }
})

// 筛选状态
const selectedDate = ref(new Date().toISOString().split('T')[0]) // 默认今天
const selectedStatus = ref('')
const selectedCategory = ref('')
const sortBy = ref('createdAt')
const sortOrder = ref('desc')

// 状态选项
const statusOptions = [
  { label: '全部状态', value: '' },
  { label: '草稿', value: 'draft' },
  { label: '已发布', value: 'published' },
  { label: '已归档', value: 'archived' }
]

// 分类选项
const categoryOptions = [
  { label: '全部分类', value: '' },
  { label: '娱乐', value: '娱乐' },
  { label: '社会', value: '社会' },
  { label: '科技', value: '科技' },
  { label: '财经', value: '财经' },
  { label: '体育', value: '体育' },
  { label: '国际', value: '国际' },
  { label: '其他', value: 'general' }
]

// 排序选项
const sortOptions = [
  { label: '创建时间', value: 'createdAt' },
  { label: '更新时间', value: 'updatedAt' },
  { label: '标题', value: 'title' },
  { label: '浏览量', value: 'views' }
]

// 分页
const currentPage = ref(1)
const pageSize = ref(20)

// 方法
const loadArticles = async () => {
  const params = {
    page: currentPage.value,
    limit: pageSize.value,
    date: selectedDate.value,
    status: selectedStatus.value,
    topic: searchQuery.value,
    sortBy: sortBy.value,
    sortOrder: sortOrder.value
  }
  await articlesStore.loadArticles(params)
}

const handleEdit = (article) => {
  // 携带当前筛选条件到编辑页面，以便返回时恢复
  const currentQuery = router.currentRoute.value.query
  router.push({
    path: `/article/edit/${article.id}`,
    query: { returnQuery: JSON.stringify(currentQuery) }
  })
}

const handleDelete = async (article) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文章"${article.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    articlesStore.deleteArticle(article.id)
    ElMessage.success('删除成功')
  } catch {
    // 用户取消删除
  }
}

const handleStatusChange = async (article, newStatus) => {
  try {
    articlesStore.updateArticle(article.id, { status: newStatus })
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
  }
}

const getStatusColor = (status) => {
  const colors = {
    draft: 'warning',
    published: 'success',
    archived: 'info'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    published: '已发布',
    archived: '已归档'
  }
  return texts[status] || status
}

const getCategoryText = (category) => {
  const option = categoryOptions.find(opt => opt.value === category)
  return option ? option.label : category
}

const formatDate = (dateString) => {
  if (!dateString) return '未知时间'
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return '无效日期'
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '日期错误'
  }
}

const handlePageChange = (page) => {
  currentPage.value = page
  updateUrlQuery()
  loadArticles()
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  updateUrlQuery()
  loadArticles()
}

const resetFilters = () => {
  articlesStore.resetFilters()
  selectedDate.value = new Date().toISOString().split('T')[0]
  selectedStatus.value = ''
  selectedCategory.value = ''
  sortBy.value = 'createdAt'
  sortOrder.value = 'desc'
  currentPage.value = 1
  loadArticles()
}

// 监听筛选条件变化，重新加载文章
watch([selectedDate, selectedStatus, selectedCategory, sortBy, sortOrder], () => {
  currentPage.value = 1
  updateUrlQuery()
  loadArticles()
  // 更新store中的筛选条件
  articlesStore.setFilters({
    status: selectedStatus.value,
    category: selectedCategory.value,
    sortBy: sortBy.value,
    sortOrder: sortOrder.value
  })
})

// 监听搜索查询变化
watch(searchQuery, () => {
  currentPage.value = 1
  loadArticles()
})

// 从URL查询参数初始化筛选条件
const initializeFromQuery = () => {
  const query = router.currentRoute.value.query

  if (query.date) {
    selectedDate.value = query.date
  }
  if (query.status) {
    selectedStatus.value = query.status
  }
  if (query.category) {
    selectedCategory.value = query.category
  }
  if (query.sortBy) {
    sortBy.value = query.sortBy
  }
  if (query.sortOrder) {
    sortOrder.value = query.sortOrder
  }
  if (query.page) {
    currentPage.value = parseInt(query.page)
  }
  if (query.pageSize) {
    pageSize.value = parseInt(query.pageSize)
  }
}

// 更新URL查询参数
const updateUrlQuery = () => {
  const query = {
    date: selectedDate.value,
    page: currentPage.value,
    pageSize: pageSize.value
  }

  if (selectedStatus.value) {
    query.status = selectedStatus.value
  }
  if (selectedCategory.value) {
    query.category = selectedCategory.value
  }
  if (sortBy.value !== 'createdAt') {
    query.sortBy = sortBy.value
  }
  if (sortOrder.value !== 'desc') {
    query.sortOrder = sortOrder.value
  }

  router.replace({ query })
}

onMounted(() => {
  initializeFromQuery()
  loadArticles()
})
</script>

<template>
  <div class="article-list">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">文章管理</h1>
        <p class="page-subtitle">管理您的所有文章内容</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" :icon="Edit" @click="router.push('/article/edit')">
          创建文章
        </el-button>
        <el-button :icon="Upload" @click="router.push('/import')">
          导入文件
        </el-button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filters-section">
      <div class="search-bar">
        <el-input
          v-model="searchQuery"
          placeholder="搜索文章标题或内容..."
          :prefix-icon="Search"
          size="large"
          clearable
        />
      </div>
      
      <div class="filters">
        <el-date-picker
          v-model="selectedDate"
          type="date"
          placeholder="选择日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          size="default"
          style="width: 160px"
        />

        <el-select v-model="selectedStatus" placeholder="状态筛选" clearable>
          <el-option
            v-for="option in statusOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>

        <el-select v-model="selectedCategory" placeholder="分类筛选" clearable>
          <el-option
            v-for="option in categoryOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        
        <el-select v-model="sortBy" placeholder="排序方式">
          <el-option
            v-for="option in sortOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </el-select>
        
        <el-button-group>
          <el-button 
            :type="sortOrder === 'asc' ? 'primary' : 'default'"
            @click="sortOrder = 'asc'"
          >
            <el-icon><Sort /></el-icon>
            升序
          </el-button>
          <el-button 
            :type="sortOrder === 'desc' ? 'primary' : 'default'"
            @click="sortOrder = 'desc'"
          >
            <el-icon><Sort /></el-icon>
            降序
          </el-button>
        </el-button-group>
        
        <el-button @click="resetFilters">重置</el-button>
      </div>
    </div>

    <!-- 文章列表 -->
    <div class="articles-container">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <div v-else-if="!articles || articles.length === 0" class="empty-container">
        <el-empty description="暂无文章数据" />
      </div>

      <div v-else class="articles-grid">
        <div
          v-for="article in articles"
          :key="article.id"
          class="article-card"
        >
          <div class="card-header">
            <div class="article-status">
              <el-tag :type="getStatusColor(article.status)" size="small">
                {{ getStatusText(article.status) }}
              </el-tag>
              <span class="article-category">{{ article.category || '未分类' }}</span>
            </div>
            <el-dropdown trigger="click">
              <el-button text :icon="MoreFilled" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item @click="handleEdit(article)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item 
                    v-if="article.status === 'draft'"
                    @click="handleStatusChange(article, 'published')"
                  >
                    <el-icon><Check /></el-icon>
                    发布
                  </el-dropdown-item>
                  <el-dropdown-item 
                    v-if="article.status === 'published'"
                    @click="handleStatusChange(article, 'archived')"
                  >
                    <el-icon><Box /></el-icon>
                    归档
                  </el-dropdown-item>
                  <el-dropdown-item 
                    divided
                    @click="handleDelete(article)"
                  >
                    <el-icon><Delete /></el-icon>
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          
          <div class="card-content" @click="handleEdit(article)">
            <h3 class="article-title">{{ article.title }}</h3>
            <p class="article-summary">{{ article.summary }}</p>

            <!-- 关键词标签 -->
            <div v-if="article.keywords && article.keywords.length > 0" class="article-keywords">
              <el-tag
                v-for="keyword in (Array.isArray(article.keywords) ? article.keywords : []).slice(0, 3)"
                :key="keyword"
                size="small"
                type="info"
                class="keyword-tag"
              >
                {{ keyword }}
              </el-tag>
            </div>
          </div>
          
          <div class="card-footer">
            <div class="article-meta">
              <span class="meta-item">
                <el-icon><Clock /></el-icon>
                {{ formatDate(article.created_at || article.createdAt) }}
              </span>
              <span class="meta-item">
                <el-icon><View /></el-icon>
                {{ article.views }} 次浏览
              </span>
              <span class="meta-item">
                <el-icon><Document /></el-icon>
                {{ article.word_count || article.wordCount || 0 }} 字
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="total > pageSize" class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<style scoped>
.article-list {
  padding: var(--spacing-xl);
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-2xl);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: 16px;
  color: var(--text-secondary);
}

.header-actions {
  display: flex;
  gap: var(--spacing-md);
}

.filters-section {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
}

.search-bar {
  margin-bottom: var(--spacing-lg);
}

.filters {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
  align-items: center;
}

.articles-container {
  margin-bottom: var(--spacing-xl);
}

.loading-container,
.empty-container {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-2xl);
}

.articles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--spacing-lg);
}

.article-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.article-card:hover {
  border-color: var(--border-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.article-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.article-category {
  font-size: 12px;
  color: var(--text-muted);
}

.card-content {
  flex: 1;
  cursor: pointer;
  margin-bottom: var(--spacing-md);
}

.article-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-summary {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-keywords {
  margin-top: var(--spacing-sm);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
}

.keyword-tag {
  font-size: 12px;
}

.card-footer {
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing-md);
}

.article-meta {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 12px;
  color: var(--text-muted);
}

.pagination-container {
  display: flex;
  justify-content: center;
  padding: var(--spacing-xl) 0;
}

@media (max-width: 768px) {
  .article-list {
    padding: var(--spacing-lg);
  }
  
  .page-header {
    flex-direction: column;
    gap: var(--spacing-lg);
  }
  
  .filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .articles-grid {
    grid-template-columns: 1fr;
  }
  
  .article-meta {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}
</style>
