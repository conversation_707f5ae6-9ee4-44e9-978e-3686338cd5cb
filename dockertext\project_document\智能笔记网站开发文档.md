# 智能笔记网站开发文档

## 项目概述

### 背景
您已经实现了完整的自动化新闻处理流程：
1. 定时收集新闻并向量化存储
2. 抓取热榜话题并合并
3. 基于话题检索相关新闻并总结
4. 自动生成高质量文章内容
5. 目前生成了200+篇优质文章

### 目标
开发一个功能完整的笔记网站，替代映像笔记，具备：
- 文章编辑和管理功能
- 本地文件导入支持
- 映像笔记的核心功能
- 便于审核和发布到自媒体平台

## 技术架构

### 前端技术栈
- **框架**: Vue 3 + Composition API
- **构建工具**: Vite
- **UI组件库**: Element Plus (推荐)
- **富文本编辑器**: Quill.js 或 TinyMCE
- **状态管理**: Pinia
- **路由**: Vue Router
- **样式**: SCSS + CSS Modules

### 后端技术栈
- **框架**: Node.js + Express 或 Fastify
- **数据库**: SQLite (轻量级) 或 PostgreSQL
- **文件存储**: 本地文件系统 + 云存储(可选)
- **搜索引擎**: 内置全文搜索 或 Elasticsearch

## 核心功能模块

### 1. 文章管理模块
- **文章列表**: 分页、搜索、筛选、排序
- **文章分类**: 标签系统、文件夹结构
- **批量操作**: 批量导入、导出、删除
- **状态管理**: 草稿、待审核、已发布、已归档

### 2. 富文本编辑器
- **所见即所得编辑**
- **Markdown支持**
- **图片上传和管理**
- **代码高亮**
- **表格编辑**
- **自动保存**

### 3. 文件导入系统
- **支持格式**: .txt, .md, .docx, .html
- **批量导入**: 支持文件夹批量导入
- **格式转换**: 自动识别和转换格式
- **元数据提取**: 自动提取标题、创建时间等

### 4. 搜索和筛选
- **全文搜索**: 标题、内容、标签搜索
- **高级筛选**: 按时间、分类、状态筛选
- **搜索历史**: 保存常用搜索条件

### 5. 发布管理
- **预览功能**: 多种样式预览
- **发布状态**: 跟踪发布到各平台的状态
- **发布历史**: 记录发布时间和平台

## 数据库设计

### 文章表 (articles)
```sql
CREATE TABLE articles (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  title VARCHAR(255) NOT NULL,
  content TEXT,
  summary TEXT,
  status ENUM('draft', 'review', 'published', 'archived'),
  category_id INTEGER,
  tags JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  published_at TIMESTAMP,
  word_count INTEGER,
  read_count INTEGER DEFAULT 0,
  source_file VARCHAR(255),
  metadata JSON
);
```

### 分类表 (categories)
```sql
CREATE TABLE categories (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  name VARCHAR(100) NOT NULL,
  parent_id INTEGER,
  description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 发布记录表 (publish_records)
```sql
CREATE TABLE publish_records (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  article_id INTEGER,
  platform VARCHAR(50),
  platform_url VARCHAR(255),
  published_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  status ENUM('success', 'failed', 'pending')
);
```

## 项目结构

```
my-vue-app/
├── src/
│   ├── components/          # 通用组件
│   │   ├── Editor/         # 富文本编辑器组件
│   │   ├── FileUpload/     # 文件上传组件
│   │   ├── ArticleCard/    # 文章卡片组件
│   │   └── SearchBar/      # 搜索组件
│   ├── views/              # 页面组件
│   │   ├── Dashboard/      # 仪表板
│   │   ├── ArticleList/    # 文章列表
│   │   ├── ArticleEdit/    # 文章编辑
│   │   ├── Import/         # 文件导入
│   │   └── Settings/       # 设置页面
│   ├── stores/             # Pinia状态管理
│   │   ├── articles.js     # 文章状态
│   │   ├── categories.js   # 分类状态
│   │   └── user.js         # 用户状态
│   ├── api/                # API接口
│   │   ├── articles.js     # 文章相关API
│   │   ├── upload.js       # 上传相关API
│   │   └── search.js       # 搜索相关API
│   ├── utils/              # 工具函数
│   │   ├── fileParser.js   # 文件解析
│   │   ├── textProcessor.js # 文本处理
│   │   └── dateUtils.js    # 日期工具
│   └── assets/             # 静态资源
├── server/                 # 后端服务
│   ├── routes/             # 路由
│   ├── models/             # 数据模型
│   ├── middleware/         # 中间件
│   └── utils/              # 后端工具
└── public/                 # 公共资源
```

## 开发阶段规划

### 第一阶段：基础框架搭建 (1-2天)
1. 安装和配置必要依赖
2. 搭建基础路由和布局
3. 设计数据库结构
4. 实现基础的CRUD API

### 第二阶段：核心功能开发 (3-4天)
1. 文章列表和详情页面
2. 富文本编辑器集成
3. 文件导入功能
4. 搜索和筛选功能

### 第三阶段：高级功能 (2-3天)
1. 批量操作功能
2. 发布管理系统
3. 用户界面优化
4. 性能优化

### 第四阶段：测试和部署 (1天)
1. 功能测试
2. 数据迁移工具
3. 部署配置

## 关键技术要点

### 1. 文件导入处理
- 使用 `mammoth.js` 处理 .docx 文件
- 使用 `marked` 处理 Markdown 文件
- 实现文本编码自动检测

### 2. 富文本编辑器选择
推荐使用 **Quill.js**：
- 轻量级，性能好
- 模块化设计，易于扩展
- 支持自定义工具栏
- 良好的移动端支持

### 3. 搜索功能实现
- 前端：实现实时搜索和搜索建议
- 后端：使用 SQLite FTS 或简单的 LIKE 查询
- 高级：集成 Elasticsearch 提供更强大的搜索

### 4. 性能优化策略
- 虚拟滚动处理大量文章列表
- 图片懒加载和压缩
- 文章内容分页加载
- 使用 Web Workers 处理文件解析

## 与现有系统集成

### 1. 文章导入
- 创建批量导入脚本，读取 `生成文章_*` 文件夹
- 解析文章格式，提取标题、内容、标签
- 自动分类和标签化

### 2. 自动化流程对接
- 提供 API 接口接收新生成的文章
- 实现文章状态自动流转
- 支持定时任务触发导入

### 3. 发布平台集成
- 预留各大自媒体平台的发布接口
- 实现一键发布到多平台
- 跟踪发布状态和效果数据

## 下一步行动计划

1. **确认技术选型**: 确认UI组件库和富文本编辑器选择
2. **环境搭建**: 安装必要的依赖包
3. **数据库设计**: 创建数据库和表结构
4. **原型开发**: 先实现一个简单的文章列表和编辑功能
5. **逐步完善**: 按阶段规划逐步添加功能

您觉得这个开发方案如何？有什么需要调整或补充的地方吗？
