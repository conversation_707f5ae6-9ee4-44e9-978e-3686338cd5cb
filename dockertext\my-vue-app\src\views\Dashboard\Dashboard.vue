<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useArticlesStore } from '../../stores/articles.js'
import { ElMessage } from 'element-plus'
import { Document, EditPen, Check, Calendar, ArrowRight } from '@element-plus/icons-vue'

const router = useRouter()
const articlesStore = useArticlesStore()

// 计算属性
const stats = computed(() => articlesStore.stats)
const recentArticles = computed(() => articlesStore.recentArticles)
const loading = computed(() => articlesStore.loading)

// 快捷操作
const quickActions = [
  {
    title: '创建新文章',
    description: '开始写作新的内容',
    icon: 'Edit',
    color: 'primary',
    action: () => router.push('/article/edit')
  },
  {
    title: '导入文件',
    description: '批量导入本地文件',
    icon: 'Upload',
    color: 'success',
    action: () => router.push('/import')
  },
  {
    title: '文章管理',
    description: '查看和管理所有文章',
    icon: 'Document',
    color: 'info',
    action: () => router.push('/articles')
  }
]

const getStatusColor = (status) => {
  return status === 'published' ? 'success' : 'warning'
}

const getStatusText = (status) => {
  return status === 'published' ? '已发布' : '草稿'
}

const formatDate = (dateString) => {
  if (!dateString) return '未知时间'
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return '无效日期'
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '日期错误'
  }
}

const refreshData = async () => {
  try {
    const today = new Date().toISOString().split('T')[0]
    await articlesStore.loadArticles({ date: today })
    await articlesStore.updateStats() // 更新统计数据
    ElMessage.success('数据刷新成功')
  } catch (error) {
    ElMessage.error('数据刷新失败')
  }
}

onMounted(async () => {
  // 加载当天文章数据
  await refreshData()
})
</script>

<template>
  <div class="dashboard">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 页面头部 -->
    <div v-else class="dashboard-header">
      <div class="header-content">
        <h1 class="page-title">仪表板</h1>
        <p class="page-subtitle">欢迎回来！管理您的智能笔记系统</p>
      </div>
      <div class="header-actions">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新数据
        </el-button>
        <el-button type="primary" :icon="Edit" @click="router.push('/article/edit')">
          创建文章
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div v-if="!loading" class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon total">
          <el-icon><Document /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.totalArticles }}</div>
          <div class="stat-label">总文章数</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon draft">
          <el-icon><EditPen /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.draftArticles }}</div>
          <div class="stat-label">草稿</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon published">
          <el-icon><Check /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.publishedArticles }}</div>
          <div class="stat-label">已发布</div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon views">
          <el-icon><Calendar /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">{{ stats.todayArticles || 0 }}</div>
          <div class="stat-label">今日文章</div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <!-- 快捷操作 -->
      <div class="section">
        <h2 class="section-title">快捷操作</h2>
        <div class="quick-actions">
          <div 
            v-for="action in quickActions" 
            :key="action.title"
            class="action-card"
            @click="action.action"
          >
            <div class="action-icon" :class="action.color">
              <el-icon>
                <component :is="action.icon" />
              </el-icon>
            </div>
            <div class="action-content">
              <h3 class="action-title">{{ action.title }}</h3>
              <p class="action-description">{{ action.description }}</p>
            </div>
            <div class="action-arrow">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近文章 -->
      <div class="section">
        <div class="section-header">
          <h2 class="section-title">最近文章</h2>
          <el-button text type="primary" @click="router.push('/articles')">
            查看全部
            <el-icon class="ml-1"><ArrowRight /></el-icon>
          </el-button>
        </div>
        
        <div class="articles-list">
          <div 
            v-for="article in recentArticles" 
            :key="article.id"
            class="article-item"
            @click="router.push(`/article/edit/${article.id}`)"
          >
            <div class="article-content">
              <h3 class="article-title">{{ article.title }}</h3>
              <div class="article-meta">
                <el-tag 
                  :type="getStatusColor(article.status)" 
                  size="small"
                >
                  {{ getStatusText(article.status) }}
                </el-tag>
                <span class="article-date">{{ formatDate(article.created_at || article.createdAt) }}</span>
                <span class="article-views">{{ article.views }} 次浏览</span>
              </div>
            </div>
            <div class="article-actions">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.dashboard {
  padding: var(--spacing-xl);
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-2xl);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: 16px;
  color: var(--text-secondary);
}

.header-actions {
  display: flex;
  gap: var(--spacing-md);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.stat-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.3s ease;
}

.stat-card:hover {
  border-color: var(--border-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.stat-icon.total {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary-color);
}

.stat-icon.draft {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.stat-icon.published {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.stat-icon.views {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  margin-top: var(--spacing-xs);
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2xl);
}

.section {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-md);
}

.action-card {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-card:hover {
  border-color: var(--border-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.action-icon.primary {
  background: rgba(99, 102, 241, 0.1);
  color: var(--primary-color);
}

.action-icon.success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.action-icon.info {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.action-content {
  flex: 1;
}

.action-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.action-description {
  font-size: 14px;
  color: var(--text-secondary);
}

.action-arrow {
  color: var(--text-muted);
  transition: all 0.2s ease;
}

.action-card:hover .action-arrow {
  color: var(--text-secondary);
  transform: translateX(2px);
}

.articles-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.article-item {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  cursor: pointer;
  transition: all 0.2s ease;
}

.article-item:hover {
  border-color: var(--border-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.article-content {
  flex: 1;
}

.article-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  line-height: 1.4;
}

.article-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 14px;
  color: var(--text-secondary);
}

.article-actions {
  color: var(--text-muted);
  transition: all 0.2s ease;
}

.article-item:hover .article-actions {
  color: var(--text-secondary);
  transform: translateX(2px);
}

@media (max-width: 768px) {
  .dashboard {
    padding: var(--spacing-lg);
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: var(--spacing-lg);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions {
    grid-template-columns: 1fr;
  }
  
  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
}
</style>
