# API 文档

智能文章管理系统 RESTful API 接口文档。

## 📋 基础信息

- **Base URL**: `http://localhost:9486/api`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 🔗 接口列表

### 1. 统计数据

#### 获取文章统计
```http
GET /api/stats
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "totalArticles": 130,
    "draftArticles": 120,
    "publishedArticles": 10,
    "archivedArticles": 0,
    "todayArticles": 5
  }
}
```

### 2. 文章管理

#### 获取文章列表
```http
GET /api/articles
```

**查询参数：**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | number | 否 | 页码，默认1 |
| limit | number | 否 | 每页数量，默认20 |
| date | string | 否 | 日期筛选 (YYYY-MM-DD) |
| status | string | 否 | 状态筛选 (draft/published/archived) |
| category | string | 否 | 分类筛选 |
| topic | string | 否 | 话题搜索 |
| sortBy | string | 否 | 排序字段，默认created_at |
| sortOrder | string | 否 | 排序方向 (asc/desc)，默认desc |

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "文章标题",
      "summary": "文章摘要",
      "category": "科技",
      "status": "published",
      "word_count": 1500,
      "created_at": "2025-07-08T10:30:00.000Z",
      "updated_at": "2025-07-08T10:30:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 130,
    "pages": 7
  }
}
```

#### 获取单篇文章
```http
GET /api/articles/:id
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "文章标题",
    "content": "<p>文章内容...</p>",
    "summary": "文章摘要",
    "category": "科技",
    "tags": ["AI", "技术"],
    "keywords": ["人工智能", "机器学习"],
    "source_urls": ["https://example.com"],
    "word_count": 1500,
    "quality_score": 8.5,
    "status": "published",
    "created_at": "2025-07-08T10:30:00.000Z",
    "updated_at": "2025-07-08T10:30:00.000Z",
    "published_at": "2025-07-08T10:30:00.000Z"
  }
}
```

#### 创建文章
```http
POST /api/articles
```

**请求体：**
```json
{
  "title": "文章标题",
  "content": "<p>文章内容...</p>",
  "summary": "文章摘要",
  "category": "科技",
  "tags": ["AI", "技术"],
  "status": "draft"
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "id": 131,
    "message": "文章创建成功"
  }
}
```

#### 更新文章
```http
PUT /api/articles/:id
```

**请求体：**
```json
{
  "title": "更新的标题",
  "content": "<p>更新的内容...</p>",
  "summary": "更新的摘要",
  "status": "published"
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "文章更新成功"
}
```

#### 删除文章
```http
DELETE /api/articles/:id
```

**响应示例：**
```json
{
  "success": true,
  "message": "文章删除成功"
}
```

### 3. 搜索功能

#### 搜索文章
```http
GET /api/search
```

**查询参数：**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| q | string | 是 | 搜索关键词 |
| page | number | 否 | 页码，默认1 |
| limit | number | 否 | 每页数量，默认20 |

**响应示例：**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "匹配的文章标题",
      "summary": "包含关键词的摘要...",
      "category": "科技",
      "status": "published",
      "created_at": "2025-07-08T10:30:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 15,
    "pages": 1
  }
}
```

## 📝 数据模型

### Article 文章模型
```typescript
interface Article {
  id: number;                    // 文章ID
  title: string;                 // 标题
  content: string;               // 内容 (HTML)
  summary: string;               // 摘要
  category: string;              // 分类
  tags: string[];                // 标签数组
  keywords: string[];            // 关键词数组
  source_urls: string[];         // 来源URL数组
  word_count: number;            // 字数
  quality_score: number;         // 质量评分 (0-10)
  status: 'draft' | 'published' | 'archived';  // 状态
  generation_method: string;     // 生成方式
  created_at: string;            // 创建时间 (ISO 8601)
  updated_at: string;            // 更新时间 (ISO 8601)
  published_at: string | null;   // 发布时间 (ISO 8601)
}
```

### Statistics 统计模型
```typescript
interface Statistics {
  totalArticles: number;         // 总文章数
  draftArticles: number;         // 草稿数
  publishedArticles: number;     // 已发布数
  archivedArticles: number;      // 已归档数
  todayArticles: number;         // 今日文章数
}
```

## ⚠️ 错误处理

### 错误响应格式
```json
{
  "success": false,
  "error": "错误信息",
  "code": "ERROR_CODE"
}
```

### 常见错误码
| 状态码 | 错误码 | 说明 |
|--------|--------|------|
| 400 | INVALID_PARAMS | 参数错误 |
| 404 | NOT_FOUND | 资源不存在 |
| 500 | INTERNAL_ERROR | 服务器内部错误 |
| 500 | DATABASE_ERROR | 数据库错误 |

### 错误示例
```json
{
  "success": false,
  "error": "文章不存在",
  "code": "NOT_FOUND"
}
```

## 🔧 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 📊 分页说明

所有列表接口都支持分页，分页信息包含在响应的 `pagination` 字段中：

```json
{
  "pagination": {
    "page": 1,        // 当前页码
    "limit": 20,      // 每页数量
    "total": 130,     // 总记录数
    "pages": 7        // 总页数
  }
}
```

## 🏷️ 分类说明

系统支持以下文章分类：
- 时政
- 娱乐
- 科技
- 体育
- 社会
- 国际
- 财经
- 其他

## 📅 日期格式

所有日期字段使用 ISO 8601 格式：
- 格式：`YYYY-MM-DDTHH:mm:ss.sssZ`
- 示例：`2025-07-08T10:30:00.000Z`
- 时区：UTC

## 🔍 搜索说明

搜索功能支持：
- 标题搜索
- 内容搜索
- 摘要搜索
- 关键词搜索

搜索使用 MySQL 的 `LIKE` 操作符，支持模糊匹配。

## 📝 使用示例

### JavaScript/Axios 示例
```javascript
// 获取文章列表
const response = await axios.get('/api/articles', {
  params: {
    page: 1,
    limit: 20,
    category: '科技',
    status: 'published'
  }
});

// 创建文章
const newArticle = await axios.post('/api/articles', {
  title: '新文章标题',
  content: '<p>文章内容</p>',
  category: '科技',
  status: 'draft'
});

// 更新文章
const updatedArticle = await axios.put('/api/articles/1', {
  title: '更新的标题',
  status: 'published'
});
```

### cURL 示例
```bash
# 获取文章列表
curl -X GET "http://localhost:9486/api/articles?page=1&limit=10"

# 创建文章
curl -X POST "http://localhost:9486/api/articles" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "新文章",
    "content": "<p>内容</p>",
    "category": "科技"
  }'

# 删除文章
curl -X DELETE "http://localhost:9486/api/articles/1"
```

---

**注意：** 本API文档基于当前版本，如有更新请及时查看最新版本。
