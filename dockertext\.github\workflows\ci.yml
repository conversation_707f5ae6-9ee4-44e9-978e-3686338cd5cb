name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    timeout-minutes: 15

    strategy:
      matrix:
        node-version: [20.x]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: my-vue-app/package-lock.json
    
    - name: Install frontend dependencies
      run: |
        cd my-vue-app
        npm install

    - name: Build frontend
      run: |
        cd my-vue-app
        npm run build
      env:
        NODE_OPTIONS: "--max-old-space-size=4096"
    
    - name: Run tests (if available)
      run: |
        # npm test
        echo "Tests would run here"

  build:
    needs: test
    runs-on: ubuntu-latest
    timeout-minutes: 20
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: 20.x
        cache: 'npm'
        cache-dependency-path: my-vue-app/package-lock.json

    - name: Install dependencies
      run: |
        cd my-vue-app
        npm install
    
    - name: Build application
      run: |
        cd my-vue-app
        npm run build
      env:
        NODE_OPTIONS: "--max-old-space-size=4096"
    
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-files
        path: my-vue-app/dist/
