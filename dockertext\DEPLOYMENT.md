# 文章管理系统 Docker 部署教程

## 📋 系统要求

### 最低配置要求
- **CPU**: 1核心
- **内存**: 1GB RAM
- **存储**: 2GB 可用空间
- **操作系统**: Linux/Windows/macOS

### 推荐配置
- **CPU**: 2核心或以上
- **内存**: 2GB RAM 或以上
- **存储**: 5GB 可用空间

### 软件依赖
- Docker Engine 20.10+
- Docker Compose 2.0+

## 🚀 快速部署

### 1. 安装 Docker 和 Docker Compose

#### Ubuntu/Debian
```bash
# 更新包索引
sudo apt update

# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装 Docker Compose
sudo apt install docker-compose-plugin

# 将用户添加到 docker 组
sudo usermod -aG docker $USER
newgrp docker
```

#### CentOS/RHEL
```bash
# 安装 Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动 Docker
sudo systemctl start docker
sudo systemctl enable docker
```

#### Windows
1. 下载并安装 [Docker Desktop for Windows](https://www.docker.com/products/docker-desktop)
2. 启动 Docker Desktop
3. 确保 WSL2 后端已启用

#### macOS
1. 下载并安装 [Docker Desktop for Mac](https://www.docker.com/products/docker-desktop)
2. 启动 Docker Desktop

### 2. 验证安装
```bash
docker --version
docker compose version
```

## 📦 项目部署

### 1. 获取项目代码
```bash
# 如果使用 Git
git clone <your-repository-url>
cd 文章管理

# 或者直接解压项目文件到目录
```

### 2. 配置环境变量
```bash
cd dockertext
cp .env.example .env
```

编辑 `.env` 文件：
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=news_system

# 应用配置
NODE_ENV=production
PORT=9485
HOST=0.0.0.0

# 时区设置
TZ=Asia/Shanghai
```

### 3. 准备数据库
确保您的 MySQL 数据库已经创建并包含必要的表结构。

### 4. 构建和启动服务
```bash
# 构建镜像并启动服务
docker compose up -d --build

# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f smart-notes
```

### 5. 验证部署
打开浏览器访问：`http://localhost:9485`

## 🔧 配置说明

### Docker Compose 配置
```yaml
services:
  smart-notes:
    build: .
    ports:
      - "9485:9485"  # 主机端口:容器端口
    environment:
      - NODE_ENV=production
      - PORT=9485
      - HOST=0.0.0.0
    env_file:
      - .env
    volumes:
      - ./data:/app/data  # 数据持久化
    restart: unless-stopped
```

### 端口配置
- **应用端口**: 9485
- **访问地址**: http://localhost:9485
- **API地址**: http://localhost:9485/api

## 📁 目录结构
```
dockertext/
├── Dockerfile              # Docker 镜像构建文件
├── docker-compose.yml      # Docker Compose 配置
├── .env                    # 环境变量配置
├── .env.example           # 环境变量模板
├── my-vue-app/            # 前端应用代码
│   ├── src/               # 源代码
│   ├── dist/              # 构建输出
│   ├── package.json       # 依赖配置
│   └── database-server.js # 后端服务器
├── data/                  # 数据目录（持久化）
└── DEPLOYMENT.md          # 部署文档
```

## 🛠️ 常用命令

### 服务管理
```bash
# 启动服务
docker compose up -d

# 停止服务
docker compose down

# 重启服务
docker compose restart

# 查看服务状态
docker compose ps

# 查看实时日志
docker compose logs -f smart-notes
```

### 镜像管理
```bash
# 重新构建镜像
docker compose build --no-cache

# 拉取最新镜像
docker compose pull

# 清理未使用的镜像
docker image prune -f
```

### 数据管理
```bash
# 备份数据目录
tar -czf backup-$(date +%Y%m%d).tar.gz data/

# 进入容器
docker compose exec smart-notes sh

# 查看容器资源使用
docker stats
```

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
netstat -tulpn | grep 9485
# 或
lsof -i :9485

# 修改端口（在 docker-compose.yml 中）
ports:
  - "9486:9485"  # 使用不同的主机端口
```

#### 2. 数据库连接失败
- 检查 `.env` 文件中的数据库配置
- 确保数据库服务正在运行
- 验证数据库用户权限

#### 3. 构建失败
```bash
# 清理 Docker 缓存
docker system prune -f

# 重新构建
docker compose build --no-cache
```

#### 4. 服务无法启动
```bash
# 查看详细日志
docker compose logs smart-notes

# 检查配置文件语法
docker compose config
```

### 日志查看
```bash
# 查看应用日志
docker compose logs smart-notes

# 实时跟踪日志
docker compose logs -f --tail=100 smart-notes

# 查看系统资源
docker stats smart-notes
```

## 🔒 安全建议

### 1. 环境变量安全
- 不要将 `.env` 文件提交到版本控制
- 使用强密码
- 定期更换敏感信息

### 2. 网络安全
```bash
# 只绑定本地接口（生产环境建议）
ports:
  - "127.0.0.1:9485:9485"
```

### 3. 数据备份
```bash
# 设置定期备份
crontab -e
# 添加：0 2 * * * /path/to/backup-script.sh
```

## 📈 性能优化

### 1. 资源限制
```yaml
services:
  smart-notes:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
```

### 2. 健康检查
```yaml
services:
  smart-notes:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9485/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

## 🔄 更新部署

### 1. 更新应用
```bash
# 拉取最新代码
git pull origin main

# 重新构建并部署
docker compose up -d --build
```

### 2. 滚动更新
```bash
# 停止旧容器
docker compose stop smart-notes

# 构建新镜像
docker compose build smart-notes

# 启动新容器
docker compose up -d smart-notes
```

## 📞 技术支持

如果遇到问题，请：
1. 查看日志文件
2. 检查配置文件
3. 参考故障排除章节
4. 联系技术支持

---

**部署完成后，您的文章管理系统将在 http://localhost:9485 上运行！**
