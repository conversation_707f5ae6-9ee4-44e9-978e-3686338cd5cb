# 服务器部署检查清单

## 📋 **服务器信息**
- **服务器IP**: `http://**************/`
- **数据库密码**: `Huangkun729.`
- **应用端口**: `9485`
- **访问地址**: `http://**************:9485`

## ✅ **部署前检查**

### 1. 服务器环境
- [ ] Docker 已安装 (`docker --version`)
- [ ] Docker Compose 已安装 (`docker compose version`)
- [ ] Docker 服务正在运行 (`docker info`)
- [ ] MySQL 服务正在运行 (`sudo systemctl status mysql`)

### 2. 数据库配置
- [ ] MySQL 用户权限正确
- [ ] 数据库 `news_system` 已创建
- [ ] 密码 `Huangkun729.` 正确（注意末尾的点）
- [ ] MySQL 允许外部连接 (`bind-address = 0.0.0.0`)

### 3. 网络配置
- [ ] 端口 9485 未被占用 (`lsof -i :9485`)
- [ ] 防火墙允许 9485 端口
- [ ] 防火墙允许 3306 端口（MySQL）

## 🚀 **快速部署步骤**

### 方法1：使用自动部署脚本
```bash
cd dockertext
./deploy-server.sh
```

### 方法2：手动部署
```bash
# 1. 复制配置文件
cp .env.production .env

# 2. 编辑配置（如需要）
nano .env

# 3. 构建和启动
docker compose down
docker compose build --no-cache
docker compose up -d

# 4. 检查状态
docker compose ps
docker compose logs -f smart-notes
```

## 🔧 **配置文件说明**

### Docker 配置 (`.env`)
```bash
# 数据库配置
DB_HOST=**********          # Docker网桥IP
DB_PORT=3306
DB_USER=root
DB_PASSWORD=Huangkun729.    # 注意末尾的点
DB_NAME=news_system

# 应用配置
NODE_ENV=production
PORT=9485
HOST=0.0.0.0

# API配置
VITE_API_BASE_URL=http://**************:9485/api
```

### 前端配置 (`my-vue-app/.env`)
```bash
# 开发环境
VITE_API_BASE_URL=http://localhost:9485/api

# 生产环境（如需要）
# VITE_API_BASE_URL=http://**************:9485/api
```

## 🔍 **部署后验证**

### 1. 服务状态检查
```bash
# 检查容器状态
docker compose ps

# 查看日志
docker compose logs smart-notes

# 检查端口监听
netstat -tlnp | grep 9485
```

### 2. API 连接测试
```bash
# 本地测试
curl http://localhost:9485/api/stats

# 外网测试
curl http://**************:9485/api/stats
```

### 3. 前端访问测试
- 浏览器访问: `http://**************:9485`
- 检查控制台是否有错误
- 测试文章列表加载
- 测试文章创建/编辑功能

## 🛠️ **常见问题解决**

### 问题1：数据库连接失败
```bash
# 检查MySQL状态
sudo systemctl status mysql

# 测试数据库连接
mysql -h localhost -u root -p
# 输入密码: Huangkun729.

# 检查用户权限
mysql -u root -p -e "SELECT user,host FROM mysql.user WHERE user='root';"
```

### 问题2：端口被占用
```bash
# 查看端口占用
lsof -i :9485

# 停止占用进程
sudo kill -9 <PID>

# 或修改端口
# 编辑 docker-compose.yml 和 .env 文件
```

### 问题3：防火墙阻止访问
```bash
# Ubuntu/Debian
sudo ufw allow 9485
sudo ufw allow 3306

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=9485/tcp
sudo firewall-cmd --permanent --add-port=3306/tcp
sudo firewall-cmd --reload
```

### 问题4：Docker网络问题
```bash
# 查看Docker网络
docker network inspect bridge

# 重启Docker服务
sudo systemctl restart docker

# 重新部署
docker compose down
docker compose up -d
```

## 📊 **监控和维护**

### 日常监控命令
```bash
# 查看服务状态
docker compose ps

# 查看资源使用
docker stats smart-notes

# 查看最新日志
docker compose logs --tail=50 smart-notes

# 重启服务
docker compose restart smart-notes
```

### 备份数据
```bash
# 备份数据目录
tar -czf backup-$(date +%Y%m%d).tar.gz data/

# 备份数据库
mysqldump -u root -p news_system > backup-$(date +%Y%m%d).sql
```

### 更新部署
```bash
# 拉取最新代码
git pull origin main

# 重新构建部署
docker compose down
docker compose build --no-cache
docker compose up -d
```

## 🔒 **安全建议**

1. **修改默认密码**
   - 更改MySQL root密码
   - 创建专用数据库用户

2. **网络安全**
   - 配置防火墙规则
   - 使用HTTPS（如有域名）
   - 限制数据库访问IP

3. **定期维护**
   - 定期备份数据
   - 监控系统资源
   - 更新Docker镜像

## 📞 **技术支持**

如遇到问题，请提供以下信息：
1. 错误日志: `docker compose logs smart-notes`
2. 服务状态: `docker compose ps`
3. 系统信息: `uname -a`
4. Docker版本: `docker --version`

---

**部署完成后，您的文章管理系统将在 http://**************:9485 上运行！**
