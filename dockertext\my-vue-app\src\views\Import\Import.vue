<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()

// 文件上传相关
const uploadRef = ref()
const fileList = ref([])
const uploading = ref(false)
const importProgress = ref(0)

// 导入设置
const importSettings = ref({
  autoCategory: true,
  autoTags: true,
  defaultStatus: 'draft',
  overwriteExisting: false,
  batchSize: 10
})

// 支持的文件类型
const acceptedTypes = ['.txt', '.md', '.docx', '.html']
const maxFileSize = 10 * 1024 * 1024 // 10MB

// 导入结果
const importResults = ref({
  total: 0,
  success: 0,
  failed: 0,
  errors: []
})

const showResults = ref(false)

// 计算属性
const canImport = computed(() => {
  return fileList.value.length > 0 && !uploading.value
})

const progressPercent = computed(() => {
  return Math.round(importProgress.value)
})

// 方法
const handleFileChange = (file, fileListParam) => {
  // 检查文件类型
  const fileName = file.name.toLowerCase()
  const isValidType = acceptedTypes.some(type => fileName.endsWith(type))
  
  if (!isValidType) {
    ElMessage.error(`不支持的文件类型。支持的格式：${acceptedTypes.join(', ')}`)
    return false
  }
  
  // 检查文件大小
  if (file.size > maxFileSize) {
    ElMessage.error('文件大小不能超过 10MB')
    return false
  }
  
  fileList.value = fileListParam
  return true
}

const handleFileRemove = (file, fileListParam) => {
  fileList.value = fileListParam
}

const parseTextFile = async (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target.result
      
      // 简单的文本解析逻辑
      const lines = content.split('\n')
      let title = ''
      let articleContent = ''
      let summary = ''
      
      // 查找标题
      for (let i = 0; i < Math.min(5, lines.length); i++) {
        const line = lines[i].trim()
        if (line && !line.startsWith('话题:') && !line.startsWith('相关新闻数:')) {
          title = line.replace(/^#+\s*/, '') // 移除markdown标题符号
          break
        }
      }
      
      // 提取内容
      let contentStartIndex = 0
      for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes('============================================================')) {
          contentStartIndex = i + 1
          break
        }
      }
      
      if (contentStartIndex > 0) {
        articleContent = lines.slice(contentStartIndex).join('\n').trim()
      } else {
        articleContent = content
      }
      
      // 生成摘要（取前200字符）
      const plainText = articleContent.replace(/<[^>]*>/g, '').replace(/[#*`]/g, '')
      summary = plainText.substring(0, 200) + (plainText.length > 200 ? '...' : '')
      
      resolve({
        title: title || file.name.replace(/\.[^/.]+$/, ''),
        content: articleContent,
        summary: summary,
        fileName: file.name,
        fileType: file.name.split('.').pop().toLowerCase()
      })
    }
    reader.readAsText(file.raw, 'utf-8')
  })
}

const parseMarkdownFile = async (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const content = e.target.result
      const lines = content.split('\n')
      
      // 查找第一个标题
      let title = ''
      for (const line of lines) {
        const match = line.match(/^#+\s*(.+)/)
        if (match) {
          title = match[1].trim()
          break
        }
      }
      
      // 生成摘要
      const plainText = content.replace(/[#*`]/g, '').replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
      const summary = plainText.substring(0, 200) + (plainText.length > 200 ? '...' : '')
      
      resolve({
        title: title || file.name.replace(/\.[^/.]+$/, ''),
        content: content,
        summary: summary,
        fileName: file.name,
        fileType: 'markdown'
      })
    }
    reader.readAsText(file.raw, 'utf-8')
  })
}

const parseFile = async (file) => {
  const fileType = file.name.split('.').pop().toLowerCase()
  
  switch (fileType) {
    case 'txt':
      return await parseTextFile(file)
    case 'md':
      return await parseMarkdownFile(file)
    case 'html':
      return await parseTextFile(file) // 简化处理
    case 'docx':
      // 这里需要专门的docx解析库，暂时用文本处理
      ElMessage.warning('DOCX文件解析功能开发中，将作为文本文件处理')
      return await parseTextFile(file)
    default:
      throw new Error(`不支持的文件类型: ${fileType}`)
  }
}

const generateCategory = (content, fileName) => {
  // 简单的分类逻辑
  const text = content.toLowerCase()
  
  if (text.includes('nba') || text.includes('篮球') || text.includes('足球') || text.includes('体育')) {
    return 'sports'
  } else if (text.includes('科技') || text.includes('ai') || text.includes('技术')) {
    return 'technology'
  } else if (text.includes('经济') || text.includes('金融') || text.includes('股市')) {
    return 'finance'
  } else if (text.includes('国际') || text.includes('外交') || text.includes('政治')) {
    return 'international'
  } else if (text.includes('明星') || text.includes('娱乐') || text.includes('电影')) {
    return 'entertainment'
  } else {
    return 'social'
  }
}

const generateTags = (content, fileName) => {
  const tags = []
  const text = content.toLowerCase()
  
  // 简单的标签提取逻辑
  const tagKeywords = {
    'NBA': ['nba', '篮球'],
    '科技': ['科技', 'ai', '人工智能'],
    '金融': ['金融', '经济', '股市'],
    '国际': ['国际', '外交'],
    '娱乐': ['娱乐', '明星', '电影'],
    '社会': ['社会', '民生']
  }
  
  for (const [tag, keywords] of Object.entries(tagKeywords)) {
    if (keywords.some(keyword => text.includes(keyword))) {
      tags.push(tag)
    }
  }
  
  return tags.slice(0, 5) // 最多5个标签
}

const startImport = async () => {
  if (!canImport.value) return
  
  try {
    await ElMessageBox.confirm(
      `确定要导入 ${fileList.value.length} 个文件吗？`,
      '确认导入',
      {
        confirmButtonText: '开始导入',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
  } catch {
    return
  }
  
  uploading.value = true
  importProgress.value = 0
  importResults.value = {
    total: fileList.value.length,
    success: 0,
    failed: 0,
    errors: []
  }
  
  const batchSize = importSettings.value.batchSize
  const batches = []
  
  // 分批处理文件
  for (let i = 0; i < fileList.value.length; i += batchSize) {
    batches.push(fileList.value.slice(i, i + batchSize))
  }
  
  try {
    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex]
      
      // 并行处理当前批次
      const batchPromises = batch.map(async (file, index) => {
        try {
          // 解析文件
          const parsedData = await parseFile(file)
          
          // 自动分类
          if (importSettings.value.autoCategory) {
            parsedData.category = generateCategory(parsedData.content, parsedData.fileName)
          }
          
          // 自动标签
          if (importSettings.value.autoTags) {
            parsedData.tags = generateTags(parsedData.content, parsedData.fileName)
          }
          
          // 设置状态
          parsedData.status = importSettings.value.defaultStatus
          
          // 模拟保存到数据库
          await new Promise(resolve => setTimeout(resolve, 200))
          
          importResults.value.success++
          
          // 更新进度
          const totalProcessed = batchIndex * batchSize + index + 1
          importProgress.value = (totalProcessed / fileList.value.length) * 100
          
          return { success: true, data: parsedData }
        } catch (error) {
          importResults.value.failed++
          importResults.value.errors.push({
            fileName: file.name,
            error: error.message
          })
          
          return { success: false, error: error.message }
        }
      })
      
      await Promise.all(batchPromises)
      
      // 批次间稍作延迟
      if (batchIndex < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100))
      }
    }
    
    ElMessage.success(`导入完成！成功 ${importResults.value.success} 个，失败 ${importResults.value.failed} 个`)
    showResults.value = true
    
  } catch (error) {
    ElMessage.error('导入过程中发生错误')
  } finally {
    uploading.value = false
    importProgress.value = 100
  }
}

const clearFiles = () => {
  fileList.value = []
  uploadRef.value?.clearFiles()
}

const resetImport = () => {
  clearFiles()
  showResults.value = false
  importProgress.value = 0
  importResults.value = {
    total: 0,
    success: 0,
    failed: 0,
    errors: []
  }
}

const goToArticles = () => {
  router.push('/articles')
}

// 快速导入本地文章
const quickImportLocal = async () => {
  try {
    await ElMessageBox.confirm(
      '这将导入您本地的生成文章文件夹中的所有文章，确定继续吗？',
      '快速导入',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )
    
    ElMessage.info('快速导入功能开发中，请使用文件上传方式导入')
  } catch {
    // 用户取消
  }
}
</script>

<template>
  <div class="import-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">文件导入</h1>
        <p class="page-subtitle">批量导入本地文件，快速构建您的文章库</p>
      </div>
      <div class="header-actions">
        <el-button @click="quickImportLocal" :icon="FolderOpened">
          快速导入本地文章
        </el-button>
      </div>
    </div>

    <!-- 导入结果 -->
    <div v-if="showResults" class="results-section">
      <div class="results-card">
        <div class="results-header">
          <h2 class="results-title">导入结果</h2>
          <el-button @click="resetImport" :icon="Refresh">
            重新导入
          </el-button>
        </div>
        
        <div class="results-stats">
          <div class="stat-item success">
            <div class="stat-number">{{ importResults.success }}</div>
            <div class="stat-label">成功</div>
          </div>
          <div class="stat-item failed">
            <div class="stat-number">{{ importResults.failed }}</div>
            <div class="stat-label">失败</div>
          </div>
          <div class="stat-item total">
            <div class="stat-number">{{ importResults.total }}</div>
            <div class="stat-label">总计</div>
          </div>
        </div>
        
        <div v-if="importResults.errors.length > 0" class="error-list">
          <h3 class="error-title">错误详情</h3>
          <div class="error-item" v-for="error in importResults.errors" :key="error.fileName">
            <span class="error-file">{{ error.fileName }}</span>
            <span class="error-message">{{ error.error }}</span>
          </div>
        </div>
        
        <div class="results-actions">
          <el-button type="primary" @click="goToArticles" :icon="Document">
            查看导入的文章
          </el-button>
        </div>
      </div>
    </div>

    <!-- 导入界面 -->
    <div v-else class="import-content">
      <!-- 文件上传区域 -->
      <div class="upload-section">
        <div class="upload-card">
          <h2 class="section-title">选择文件</h2>
          <p class="section-subtitle">
            支持格式：{{ acceptedTypes.join(', ') }} | 单个文件最大 10MB
          </p>
          
          <el-upload
            ref="uploadRef"
            v-model:file-list="fileList"
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :accept="acceptedTypes.join(',')"
            multiple
            drag
            class="upload-dragger"
          >
            <div class="upload-content">
              <el-icon class="upload-icon"><UploadFilled /></el-icon>
              <div class="upload-text">
                <p class="upload-title">拖拽文件到此处，或点击选择文件</p>
                <p class="upload-hint">支持批量选择多个文件</p>
              </div>
            </div>
          </el-upload>
          
          <div v-if="fileList.length > 0" class="file-actions">
            <el-button @click="clearFiles" :icon="Delete">
              清空文件
            </el-button>
            <span class="file-count">已选择 {{ fileList.length }} 个文件</span>
          </div>
        </div>
      </div>

      <!-- 导入设置 -->
      <div class="settings-section">
        <div class="settings-card">
          <h2 class="section-title">导入设置</h2>
          
          <div class="setting-group">
            <div class="setting-item">
              <el-checkbox v-model="importSettings.autoCategory">
                自动分类
              </el-checkbox>
              <span class="setting-desc">根据文章内容自动识别分类</span>
            </div>
            
            <div class="setting-item">
              <el-checkbox v-model="importSettings.autoTags">
                自动标签
              </el-checkbox>
              <span class="setting-desc">根据文章内容自动生成标签</span>
            </div>
            
            <div class="setting-item">
              <el-checkbox v-model="importSettings.overwriteExisting">
                覆盖已存在的文章
              </el-checkbox>
              <span class="setting-desc">如果标题相同，覆盖原有文章</span>
            </div>
          </div>
          
          <div class="setting-group">
            <div class="setting-item">
              <label class="setting-label">默认状态</label>
              <el-select v-model="importSettings.defaultStatus" style="width: 150px">
                <el-option label="草稿" value="draft" />
                <el-option label="已发布" value="published" />
              </el-select>
            </div>
            
            <div class="setting-item">
              <label class="setting-label">批处理大小</label>
              <el-input-number
                v-model="importSettings.batchSize"
                :min="1"
                :max="50"
                style="width: 150px"
              />
              <span class="setting-desc">每批处理的文件数量</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 导入按钮和进度 -->
      <div class="import-actions">
        <div class="actions-card">
          <div v-if="uploading" class="progress-section">
            <div class="progress-info">
              <span class="progress-text">正在导入文件...</span>
              <span class="progress-percent">{{ progressPercent }}%</span>
            </div>
            <el-progress :percentage="progressPercent" :show-text="false" />
          </div>
          
          <div v-else class="action-buttons">
            <el-button
              type="primary"
              size="large"
              :disabled="!canImport"
              @click="startImport"
              :icon="Upload"
            >
              开始导入 ({{ fileList.length }} 个文件)
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.import-page {
  padding: var(--spacing-xl);
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-2xl);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.page-subtitle {
  font-size: 16px;
  color: var(--text-secondary);
}

.header-actions {
  display: flex;
  gap: var(--spacing-md);
}

.results-section {
  margin-bottom: var(--spacing-2xl);
}

.results-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.results-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.results-stats {
  display: flex;
  gap: var(--spacing-xl);
  margin-bottom: var(--spacing-lg);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
}

.stat-item.success .stat-number {
  color: var(--success-color);
}

.stat-item.failed .stat-number {
  color: var(--error-color);
}

.stat-item.total .stat-number {
  color: var(--primary-color);
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.error-list {
  margin-bottom: var(--spacing-lg);
}

.error-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.error-item {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-sm);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
}

.error-file {
  font-weight: 500;
  color: var(--text-primary);
}

.error-message {
  color: var(--error-color);
  font-size: 12px;
}

.results-actions {
  text-align: center;
}

.import-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.upload-section,
.settings-section,
.import-actions {
  width: 100%;
}

.upload-card,
.settings-card,
.actions-card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.section-subtitle {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

.upload-dragger {
  width: 100%;
}

.upload-dragger :deep(.el-upload-dragger) {
  width: 100%;
  height: 200px;
  background: var(--bg-tertiary);
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-dragger :deep(.el-upload-dragger:hover) {
  border-color: var(--primary-color);
}

.upload-content {
  text-align: center;
}

.upload-icon {
  font-size: 48px;
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
}

.upload-title {
  font-size: 16px;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.upload-hint {
  font-size: 14px;
  color: var(--text-secondary);
}

.file-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.file-count {
  font-size: 14px;
  color: var(--text-secondary);
}

.setting-group {
  margin-bottom: var(--spacing-lg);
}

.setting-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.setting-label {
  min-width: 80px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.setting-desc {
  font-size: 12px;
  color: var(--text-muted);
  margin-left: auto;
}

.progress-section {
  margin-bottom: var(--spacing-lg);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.progress-text {
  font-size: 14px;
  color: var(--text-primary);
}

.progress-percent {
  font-size: 14px;
  font-weight: 600;
  color: var(--primary-color);
}

.action-buttons {
  text-align: center;
}

@media (max-width: 768px) {
  .import-page {
    padding: var(--spacing-lg);
  }
  
  .page-header {
    flex-direction: column;
    gap: var(--spacing-lg);
  }
  
  .results-stats {
    justify-content: space-around;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  
  .setting-desc {
    margin-left: 0;
  }
}
</style>
