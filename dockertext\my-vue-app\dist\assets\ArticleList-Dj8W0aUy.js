import{_ as ce,c as D,v as w,x as E,o as ve,a as i,b as n,e as o,d as O,f as a,w as s,g as r,u,h as _e,y as L,r as d,z as pe,A as me,F as x,m as z,B as R,t as g,C as fe,q as T,k as ye,D as ge,G as be,H as ke,I as he,i as we,E as $,J as Ce}from"./index-BpJfbBiC.js";import{u as Se}from"./articles-B88tJUEo.js";const Ve={class:"article-list"},xe={class:"page-header"},ze={class:"header-actions"},Ae={class:"filters-section"},Be={class:"search-bar"},De={class:"filters"},Oe={class:"articles-container"},$e={key:0,class:"loading-container"},Ne={key:1,class:"empty-container"},Ye={key:2,class:"articles-grid"},Me={class:"card-header"},Qe={class:"article-status"},Te={class:"article-category"},qe=["onClick"],Fe={class:"article-title"},Ie={class:"article-summary"},Ue={key:0,class:"article-keywords"},Ee={class:"card-footer"},Le={class:"article-meta"},Re={class:"meta-item"},Je={class:"meta-item"},Pe={class:"meta-item"},je={key:0,class:"pagination-container"},Ge={__name:"ArticleList",setup(He){const C=_e(),v=Se(),N=D(()=>v.articles),J=D(()=>v.loading),q=D(()=>v.pagination.total),A=D({get:()=>v.searchQuery,set:l=>{v.setSearchQuery(l),_.value=1}}),b=w(new Date().toISOString().split("T")[0]),p=w(""),y=w(""),m=w("createdAt"),c=w("desc"),P=[{label:"全部状态",value:""},{label:"草稿",value:"draft"},{label:"已发布",value:"published"},{label:"已归档",value:"archived"}],j=[{label:"全部分类",value:""},{label:"时政",value:"时政"},{label:"娱乐",value:"娱乐"},{label:"科技",value:"科技"},{label:"体育",value:"体育"},{label:"社会",value:"社会"},{label:"国际",value:"国际"},{label:"财经",value:"财经"},{label:"其他",value:"其他"}],G=[{label:"创建时间",value:"createdAt"},{label:"更新时间",value:"updatedAt"},{label:"标题",value:"title"},{label:"浏览量",value:"views"}],_=w(1),S=w(20),V=async()=>{const l={page:_.value,limit:S.value,date:b.value,status:p.value,topic:A.value,sortBy:m.value,sortOrder:c.value};await v.loadArticles(l)},F=l=>{const e=C.currentRoute.value.query;C.push({path:`/article/edit/${l.id}`,query:{returnQuery:JSON.stringify(e)}})},H=async l=>{try{await Ce.confirm(`确定要删除文章"${l.title}"吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await v.deleteArticle(l.id),$.success("删除成功")}catch(e){e!=="cancel"&&$.error(e.message||"删除失败")}},I=async(l,e)=>{try{v.updateArticle(l.id,{status:e}),$.success("状态更新成功")}catch{$.error("状态更新失败")}},K=l=>({draft:"warning",published:"success",archived:"info"})[l]||"default",W=l=>({draft:"草稿",published:"已发布",archived:"已归档"})[l]||l,X=l=>{if(!l)return"未知时间";try{const e=new Date(l);return isNaN(e.getTime())?"无效日期":e.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})}catch{return"日期错误"}},Z=l=>{_.value=l,Y(),V()},ee=l=>{S.value=l,_.value=1,Y(),V()},te=()=>{v.resetFilters(),b.value=new Date().toISOString().split("T")[0],p.value="",y.value="",m.value="createdAt",c.value="desc",_.value=1,V()};E([b,p,y,m,c],()=>{_.value=1,Y(),V(),v.setFilters({status:p.value,category:y.value,sortBy:m.value,sortOrder:c.value})}),E(A,()=>{_.value=1,V()});const le=()=>{const l=C.currentRoute.value.query;l.date&&(b.value=l.date),l.status&&(p.value=l.status),l.category&&(y.value=l.category),l.sortBy&&(m.value=l.sortBy),l.sortOrder&&(c.value=l.sortOrder),l.page&&(_.value=parseInt(l.page)),l.pageSize&&(S.value=parseInt(l.pageSize))},Y=()=>{const l={date:b.value,page:_.value,pageSize:S.value};p.value&&(l.status=p.value),y.value&&(l.category=y.value),m.value!=="createdAt"&&(l.sortBy=m.value),c.value!=="desc"&&(l.sortOrder=c.value),C.replace({query:l})};return ve(()=>{le(),V()}),(l,e)=>{const k=d("el-button"),ae=d("el-input"),se=d("el-date-picker"),M=d("el-option"),Q=d("el-select"),f=d("el-icon"),oe=d("el-button-group"),ne=d("el-skeleton"),ue=d("el-empty"),U=d("el-tag"),B=d("el-dropdown-item"),re=d("el-dropdown-menu"),de=d("el-dropdown"),ie=d("el-pagination");return n(),i("div",Ve,[o("div",xe,[e[11]||(e[11]=o("div",{class:"header-content"},[o("h1",{class:"page-title"},"文章管理"),o("p",{class:"page-subtitle"},"管理您的所有文章内容")],-1)),o("div",ze,[a(k,{type:"primary",icon:u(L),onClick:e[0]||(e[0]=t=>u(C).push("/article/edit"))},{default:s(()=>e[9]||(e[9]=[r(" 创建文章 ")])),_:1,__:[9]},8,["icon"]),a(k,{icon:u(pe),onClick:e[1]||(e[1]=t=>u(C).push("/import"))},{default:s(()=>e[10]||(e[10]=[r(" 导入文件 ")])),_:1,__:[10]},8,["icon"])])]),o("div",Ae,[o("div",Be,[a(ae,{modelValue:A.value,"onUpdate:modelValue":e[2]||(e[2]=t=>A.value=t),placeholder:"搜索文章标题或内容...","prefix-icon":u(me),size:"large",clearable:""},null,8,["modelValue","prefix-icon"])]),o("div",De,[a(se,{modelValue:b.value,"onUpdate:modelValue":e[3]||(e[3]=t=>b.value=t),type:"date",placeholder:"选择日期",format:"YYYY-MM-DD","value-format":"YYYY-MM-DD",size:"default",style:{width:"160px"}},null,8,["modelValue"]),a(Q,{modelValue:p.value,"onUpdate:modelValue":e[4]||(e[4]=t=>p.value=t),placeholder:"状态筛选",clearable:""},{default:s(()=>[(n(),i(x,null,z(P,t=>a(M,{key:t.value,label:t.label,value:t.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),a(Q,{modelValue:y.value,"onUpdate:modelValue":e[5]||(e[5]=t=>y.value=t),placeholder:"分类筛选",clearable:""},{default:s(()=>[(n(),i(x,null,z(j,t=>a(M,{key:t.value,label:t.label,value:t.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),a(Q,{modelValue:m.value,"onUpdate:modelValue":e[6]||(e[6]=t=>m.value=t),placeholder:"排序方式"},{default:s(()=>[(n(),i(x,null,z(G,t=>a(M,{key:t.value,label:t.label,value:t.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"]),a(oe,null,{default:s(()=>[a(k,{type:c.value==="asc"?"primary":"default",onClick:e[7]||(e[7]=t=>c.value="asc")},{default:s(()=>[a(f,null,{default:s(()=>[a(u(R))]),_:1}),e[12]||(e[12]=r(" 升序 "))]),_:1,__:[12]},8,["type"]),a(k,{type:c.value==="desc"?"primary":"default",onClick:e[8]||(e[8]=t=>c.value="desc")},{default:s(()=>[a(f,null,{default:s(()=>[a(u(R))]),_:1}),e[13]||(e[13]=r(" 降序 "))]),_:1,__:[13]},8,["type"])]),_:1}),a(k,{onClick:te},{default:s(()=>e[14]||(e[14]=[r("重置")])),_:1,__:[14]})])]),o("div",Oe,[J.value?(n(),i("div",$e,[a(ne,{rows:5,animated:""})])):!N.value||N.value.length===0?(n(),i("div",Ne,[a(ue,{description:"暂无文章数据"})])):(n(),i("div",Ye,[(n(!0),i(x,null,z(N.value,t=>(n(),i("div",{key:t.id,class:"article-card"},[o("div",Me,[o("div",Qe,[a(U,{type:K(t.status),size:"small"},{default:s(()=>[r(g(W(t.status)),1)]),_:2},1032,["type"]),o("span",Te,g(t.category||"未分类"),1)]),a(de,{trigger:"click"},{dropdown:s(()=>[a(re,null,{default:s(()=>[a(B,{onClick:h=>F(t)},{default:s(()=>[a(f,null,{default:s(()=>[a(u(L))]),_:1}),e[15]||(e[15]=r(" 编辑 "))]),_:2,__:[15]},1032,["onClick"]),t.status==="draft"?(n(),T(B,{key:0,onClick:h=>I(t,"published")},{default:s(()=>[a(f,null,{default:s(()=>[a(u(ye))]),_:1}),e[16]||(e[16]=r(" 发布 "))]),_:2,__:[16]},1032,["onClick"])):O("",!0),t.status==="published"?(n(),T(B,{key:1,onClick:h=>I(t,"archived")},{default:s(()=>[a(f,null,{default:s(()=>[a(u(ge))]),_:1}),e[17]||(e[17]=r(" 归档 "))]),_:2,__:[17]},1032,["onClick"])):O("",!0),a(B,{divided:"",onClick:h=>H(t)},{default:s(()=>[a(f,null,{default:s(()=>[a(u(be))]),_:1}),e[18]||(e[18]=r(" 删除 "))]),_:2,__:[18]},1032,["onClick"])]),_:2},1024)]),default:s(()=>[a(k,{text:"",icon:u(fe)},null,8,["icon"])]),_:2},1024)]),o("div",{class:"card-content",onClick:h=>F(t)},[o("h3",Fe,g(t.title),1),o("p",Ie,g(t.summary),1),t.keywords&&t.keywords.length>0?(n(),i("div",Ue,[(n(!0),i(x,null,z((Array.isArray(t.keywords)?t.keywords:[]).slice(0,3),h=>(n(),T(U,{key:h,size:"small",type:"info",class:"keyword-tag"},{default:s(()=>[r(g(h),1)]),_:2},1024))),128))])):O("",!0)],8,qe),o("div",Ee,[o("div",Le,[o("span",Re,[a(f,null,{default:s(()=>[a(u(ke))]),_:1}),r(" "+g(X(t.created_at||t.createdAt)),1)]),o("span",Je,[a(f,null,{default:s(()=>[a(u(he))]),_:1}),r(" "+g(t.views)+" 次浏览 ",1)]),o("span",Pe,[a(f,null,{default:s(()=>[a(u(we))]),_:1}),r(" "+g(t.word_count||t.wordCount||0)+" 字 ",1)])])])]))),128))]))]),q.value>S.value?(n(),i("div",je,[a(ie,{"current-page":_.value,"page-size":S.value,total:q.value,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",background:"",onCurrentChange:Z,onSizeChange:ee},null,8,["current-page","page-size","total"])])):O("",!0)])}}},Xe=ce(Ge,[["__scopeId","data-v-b9578568"]]);export{Xe as default};
