# Docker 数据库连接配置指南

## 🚨 重要提醒

**在Docker容器中，绝对不能使用 `DB_HOST=localhost`！**

容器内的 `localhost` 指向容器内部，无法访问宿主机上的MySQL服务。

## 📋 配置方案对比

| 方案 | 适用场景 | DB_HOST配置 | 优缺点 |
|------|----------|-------------|--------|
| 方案1 | host网络模式 | `localhost` | ✅简单稳定 ✅无网络问题 ❌失去容器网络隔离 |
| 方案2 | Linux服务器 | `**********` | ✅简单 ❌IP可能变化 ❌需要MySQL绑定0.0.0.0 |
| 方案3 | Windows/Mac | `host.docker.internal` | ✅稳定 ❌仅限Docker Desktop |
| 方案4 | 任何环境 | 服务器实际IP | ✅通用 ❌需要知道IP ❌需要MySQL绑定0.0.0.0 |
| 方案5 | 完整Docker栈 | `mysql` | ✅完全隔离 ❌复杂 |

## 🔧 详细配置方法

### 方案1：使用Docker网桥IP（推荐Linux）

```bash
# 查找Docker网桥IP
docker network inspect bridge | grep Gateway

# 配置.env文件
DB_HOST=**********
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=news_system
```

**使用docker-compose.yml（默认网络）**

### 方案2：使用host.docker.internal（推荐Windows/Mac）

```bash
# 配置.env文件
DB_HOST=host.docker.internal
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=news_system
```

**使用docker-compose.yml（默认网络）**

### 方案3：使用服务器实际IP

```bash
# 查看服务器IP
ip addr show
# 或
hostname -I

# 配置.env文件
DB_HOST=*************  # 替换为实际IP
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=news_system
```

### 方案4：使用host网络模式

**docker-compose.yml配置：**
```yaml
services:
  smart-notes:
    build: .
    network_mode: "host"  # 使用宿主机网络
    environment:
      - NODE_ENV=production
      - PORT=9485
      - HOST=0.0.0.0
    env_file:
      - .env
```

**此时可以使用：**
```bash
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=news_system
```

### 方案5：完整Docker栈（包含MySQL）

**使用docker-compose.full.yml：**
```bash
# 启动完整栈
docker compose -f docker-compose.full.yml up -d

# 配置会自动设置为：
DB_HOST=mysql  # 使用服务名
```

## 🛠️ 部署步骤

### 步骤1：选择配置方案
根据您的环境选择上述方案之一。

### 步骤2：配置环境文件
```bash
# 复制生产环境配置
cp .env.production .env

# 编辑配置文件
nano .env
```

### 步骤3：测试数据库连接
```bash
# 从容器内测试连接
docker compose exec smart-notes sh
# 在容器内执行
ping $DB_HOST
telnet $DB_HOST 3306
```

### 步骤4：部署应用
```bash
# 标准部署
docker compose up -d

# 或使用完整栈
docker compose -f docker-compose.full.yml up -d
```

## 🔍 故障排除

### 问题1：连接被拒绝
```bash
# 检查MySQL是否运行
sudo systemctl status mysql

# 检查MySQL绑定地址
sudo netstat -tlnp | grep 3306

# MySQL配置文件 /etc/mysql/mysql.conf.d/mysqld.cnf
bind-address = 0.0.0.0  # 允许外部连接
```

### 问题2：权限被拒绝
```sql
-- 在MySQL中执行
CREATE USER 'root'@'%' IDENTIFIED BY 'root';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'%';
FLUSH PRIVILEGES;
```

### 问题3：防火墙阻止
```bash
# Ubuntu/Debian
sudo ufw allow 3306

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=3306/tcp
sudo firewall-cmd --reload
```

### 问题4：Docker网络问题
```bash
# 查看Docker网络
docker network ls
docker network inspect bridge

# 重启Docker网络
sudo systemctl restart docker
```

## 📝 配置验证

### 验证脚本
```bash
#!/bin/bash
# test-db-connection.sh

echo "测试数据库连接..."

# 方法1：使用telnet
if command -v telnet &> /dev/null; then
    echo "测试端口连通性..."
    timeout 5 telnet $DB_HOST $DB_PORT
fi

# 方法2：使用nc
if command -v nc &> /dev/null; then
    echo "测试端口连通性..."
    nc -zv $DB_HOST $DB_PORT
fi

# 方法3：在容器内测试
echo "在容器内测试..."
docker compose exec smart-notes sh -c "
    echo 'Testing connection to $DB_HOST:$DB_PORT'
    nc -zv $DB_HOST $DB_PORT 2>&1
"
```

### 应用日志检查
```bash
# 查看应用启动日志
docker compose logs smart-notes | grep -i database

# 实时监控日志
docker compose logs -f smart-notes
```

## 🔒 安全建议

1. **不要使用root用户**
```sql
CREATE USER 'article_user'@'%' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON news_system.* TO 'article_user'@'%';
```

2. **限制网络访问**
```yaml
# 只允许特定网络
networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

3. **使用环境变量**
```bash
# 不要在代码中硬编码密码
DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
```

## 📞 常见问题

**Q: 为什么不能用localhost？**
A: Docker容器有自己的网络命名空间，localhost指向容器内部。

**Q: **********是什么？**
A: Docker默认网桥的网关IP，容器通过它访问宿主机。

**Q: host.docker.internal在Linux上不工作？**
A: 这是Docker Desktop特有的功能，Linux需要使用其他方案。

**Q: 如何确认配置正确？**
A: 查看应用日志，确保没有数据库连接错误。

---

**记住：Docker容器中的数据库连接配置是部署成功的关键！**
