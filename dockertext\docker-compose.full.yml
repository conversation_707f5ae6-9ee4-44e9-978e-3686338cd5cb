version: '3.8'

services:
  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    container_name: article-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: news_system
      MYSQL_USER: article_user
      MYSQL_PASSWORD: article_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # 文章管理应用
  smart-notes:
    build: .
    container_name: article-app
    depends_on:
      mysql:
        condition: service_healthy
    ports:
      - "9485:9485"
    environment:
      - NODE_ENV=production
      - PORT=9485
      - HOST=0.0.0.0
      - DB_HOST=mysql  # 使用服务名作为主机名
      - DB_PORT=3306
      - DB_USER=root
      - DB_PASSWORD=root
      - DB_NAME=news_system
    volumes:
      - ./data:/app/data
      - ./生成文章_20250629_174526:/app/生成文章_20250629_174526
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9485/api/stats"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mysql_data:
    driver: local
