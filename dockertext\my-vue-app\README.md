# 智能文章管理系统

一个基于 Vue 3 + Express.js + MySQL 的现代化文章管理系统，提供完整的文章创建、编辑、管理和发布功能。

## 📋 项目概述

智能文章管理系统是一个全栈Web应用，专为内容创作者和编辑团队设计。系统提供直观的用户界面、强大的编辑功能和完善的文章管理工具。

### ✨ 核心功能

- **📝 富文本编辑器** - 基于Quill.js的强大编辑器，支持实时字数统计和自动保存
- **🏷️ 分类管理** - 支持时政、娱乐、科技、体育、社会、国际、财经等分类
- **🔍 智能搜索** - 支持标题、内容、关键词的全文搜索
- **📊 数据统计** - 实时显示文章总数、草稿数、发布数等统计信息
- **⌨️ 快捷操作** - 支持Ctrl+S保存、Ctrl+Enter发布等键盘快捷键
- **💾 自动保存** - 30秒自动保存草稿，防止内容丢失
- **📱 响应式设计** - 适配桌面端和移动端设备

### 🛠️ 技术栈

**前端技术：**
- Vue 3 (Composition API)
- Vite (构建工具)
- Element Plus (UI组件库)
- Pinia (状态管理)
- Vue Router (路由管理)
- Quill.js (富文本编辑器)
- Axios (HTTP客户端)

**后端技术：**
- Node.js
- Express.js (Web框架)
- MySQL (数据库)
- mysql2 (数据库驱动)

**开发工具：**
- ESLint (代码检查)
- Prettier (代码格式化)
- Git (版本控制)

## 🔧 系统要求

### 最低配置
- **操作系统**: Ubuntu 20.04+ / Windows 10+ / macOS 10.15+
- **Node.js**: 16.0.0+
- **MySQL**: 8.0+
- **内存**: 2GB RAM
- **存储**: 1GB 可用空间

### 推荐配置
- **操作系统**: Ubuntu 22.04 LTS
- **Node.js**: 18.0.0+
- **MySQL**: 8.0.32+
- **内存**: 4GB RAM
- **存储**: 5GB 可用空间

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd my-vue-app
```

### 2. 安装依赖
```bash
npm install
```

### 3. 配置环境变量
创建 `.env` 文件：
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=news_system

# 服务器配置
PORT=9486
NODE_ENV=development
```

创建前端环境变量 `.env`：
```bash
# API配置
VITE_API_BASE_URL=http://localhost:9486/api
```

### 4. 数据库设置
```sql
-- 创建数据库
CREATE DATABASE news_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建文章表
USE news_system;
CREATE TABLE articles (
  id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  content LONGTEXT,
  summary TEXT,
  category VARCHAR(50),
  tags JSON,
  keywords JSON,
  source_urls JSON,
  word_count INT DEFAULT 0,
  quality_score DECIMAL(3,2) DEFAULT 0,
  status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
  generation_method VARCHAR(50) DEFAULT 'manual',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  published_at TIMESTAMP NULL
);
```

### 5. 启动开发服务器
```bash
# 启动后端服务器
node database-server.js

# 启动前端开发服务器（新终端）
npm run dev
```

### 6. 构建生产版本
```bash
npm run build
```

## 📁 项目结构

```
my-vue-app/
├── src/                    # 前端源码
│   ├── api/               # API接口
│   ├── components/        # 公共组件
│   ├── stores/           # Pinia状态管理
│   ├── views/            # 页面组件
│   ├── router/           # 路由配置
│   └── main.js           # 入口文件
├── dist/                  # 构建输出
├── database-server.js     # 后端服务器
├── package.json          # 项目配置
└── README.md             # 项目文档
```

## 🔗 相关链接

- [Ubuntu部署教程](./docs/ubuntu-deployment.md)
- [API文档](./docs/api-documentation.md)
- [开发指南](./docs/development-guide.md)

## 📄 许可证

MIT License
