{"name": "my-vue-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24.0.7", "@vueup/vue-quill": "^1.2.0", "@vueuse/core": "^13.4.0", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.4.5", "element-plus": "^2.10.2", "express": "^5.1.0", "file-saver": "^2.0.5", "framer-motion": "^12.19.2", "gsap": "^3.13.0", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "lucide-vue-next": "^0.525.0", "mysql2": "^3.6.5", "pinia": "^3.0.3", "postcss": "^8.5.6", "quill": "^2.0.3", "sass": "^1.89.2", "tailwindcss": "^4.1.11", "vue": "^3.5.17", "vue-quill-editor": "^3.0.6", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "vite": "^7.0.0"}}