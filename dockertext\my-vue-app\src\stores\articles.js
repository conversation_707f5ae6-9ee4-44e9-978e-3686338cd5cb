import { defineStore } from 'pinia'
import { loadLocalArticles, loadArticleFile, getArticleStats, searchArticles, deleteArticle } from '../api/articles.js'

export const useArticlesStore = defineStore('articles', {
  state: () => ({
    // 文章列表
    articles: [],
    currentArticle: null,

    // 当前查询日期
    currentDate: new Date().toISOString().split('T')[0],

    // 统计数据
    stats: {
      totalArticles: 0,
      draftArticles: 0,
      publishedArticles: 0,
      todayViews: 0,
      categories: {}
    },

    // 加载状态
    loading: false,
    error: null,

    // 搜索和筛选
    searchQuery: '',
    filters: {
      status: '',
      category: '',
      sortBy: 'createdAt',
      sortOrder: 'desc'
    },

    // 分页
    pagination: {
      currentPage: 1,
      pageSize: 20,
      total: 0
    }
  }),
  
  getters: {
    // 过滤后的文章列表
    filteredArticles: (state) => {
      if (!state.articles || !Array.isArray(state.articles)) {
        return []
      }

      let results = [...state.articles]
      
      // 搜索过滤
      if (state.searchQuery) {
        const query = state.searchQuery.toLowerCase()
        results = results.filter(article => 
          article.title.toLowerCase().includes(query) ||
          article.topic.toLowerCase().includes(query) ||
          article.summary.toLowerCase().includes(query) ||
          article.tags.some(tag => tag.toLowerCase().includes(query))
        )
      }
      
      // 状态过滤
      if (state.filters.status) {
        results = results.filter(article => article.status === state.filters.status)
      }
      
      // 分类过滤
      if (state.filters.category) {
        results = results.filter(article => article.category === state.filters.category)
      }
      
      // 排序
      results.sort((a, b) => {
        const aValue = a[state.filters.sortBy]
        const bValue = b[state.filters.sortBy]
        
        if (state.filters.sortOrder === 'asc') {
          return aValue > bValue ? 1 : -1
        } else {
          return aValue < bValue ? 1 : -1
        }
      })
      
      return results
    },
    
    // 最近文章
    recentArticles: (state) => {
      if (!state.articles || !Array.isArray(state.articles)) {
        return []
      }

      return state.articles
        .slice()
        .sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
        .slice(0, 5)
    },
    
    // 按分类统计
    articlesByCategory: (state) => {
      if (!state.articles || !Array.isArray(state.articles)) {
        return {}
      }

      const categories = {}
      state.articles.forEach(article => {
        if (!categories[article.category]) {
          categories[article.category] = 0
        }
        categories[article.category]++
      })
      return categories
    },
    
    // 按状态统计
    articlesByStatus: (state) => {
      if (!state.articles || !Array.isArray(state.articles)) {
        return {}
      }

      const statuses = {}
      state.articles.forEach(article => {
        if (!statuses[article.status]) {
          statuses[article.status] = 0
        }
        statuses[article.status]++
      })
      return statuses
    }
  },
  
  actions: {
    // 加载文章列表
    async loadArticles(params = {}) {
      this.loading = true
      this.error = null

      try {
        console.log('[Store] 开始加载文章列表', params)
        const response = await loadLocalArticles(params)

        if (response.success) {
          console.log(`[Store] 成功加载 ${response.data.length} 篇文章`)
          this.articles = response.data
          this.pagination.total = response.total

          // 保存当前查询的日期
          if (response.currentDate) {
            this.currentDate = response.currentDate
          }

          // 更新统计数据
          this.updateStats()
        } else {
          console.error('[Store] 加载文章失败:', response.error)
          this.error = response.error
        }
      } catch (error) {
        console.error('[Store] 加载文章异常:', error)
        this.error = error.message
      } finally {
        this.loading = false
      }
    },
    
    // 加载单个文章
    async loadArticle(articleId) {
      this.loading = true
      this.error = null

      try {
        console.log(`[Store] 加载文章详情 ID: ${articleId}`)
        const response = await loadArticleFile(articleId)

        if (response.success) {
          this.currentArticle = response.data
          console.log(`[Store] 文章详情加载成功`)
          return response.data
        } else {
          console.error(`[Store] 文章详情加载失败:`, response.error)
          this.error = response.error
          return null
        }
      } catch (error) {
        console.error(`[Store] 文章详情加载异常:`, error)
        this.error = error.message
        return null
      } finally {
        this.loading = false
      }
    },
    
    // 搜索文章
    async searchArticles(query, filters = {}) {
      this.loading = true
      this.error = null
      
      try {
        const response = await searchArticles(query, filters)
        
        if (response.success) {
          this.articles = response.data
          this.pagination.total = response.total
          this.searchQuery = query
          this.filters = { ...this.filters, ...filters }
        } else {
          this.error = response.error
        }
      } catch (error) {
        this.error = error.message
      } finally {
        this.loading = false
      }
    },
    
    // 更新统计数据
    async updateStats() {
      try {
        const response = await getArticleStats()
        if (response.success) {
          this.stats = {
            totalArticles: response.data.totalArticles,
            draftArticles: response.data.draftArticles,
            publishedArticles: response.data.publishedArticles,
            archivedArticles: response.data.archivedArticles,
            todayArticles: response.data.todayArticles,
            categories: this.articlesByCategory
          }
          console.log('[Store] 统计数据更新:', this.stats)
        } else {
          console.error('[Store] 获取统计数据失败:', response.error)
        }
      } catch (error) {
        console.error('[Store] 更新统计数据异常:', error)
      }
    },
    
    // 设置搜索查询
    setSearchQuery(query) {
      this.searchQuery = query
    },
    
    // 设置过滤器
    setFilters(filters) {
      this.filters = { ...this.filters, ...filters }
    },
    
    // 重置过滤器
    resetFilters() {
      this.searchQuery = ''
      this.filters = {
        status: '',
        category: '',
        sortBy: 'createdAt',
        sortOrder: 'desc'
      }
    },
    
    // 设置分页
    setPagination(pagination) {
      this.pagination = { ...this.pagination, ...pagination }
    },
    
    // 添加文章
    addArticle(article) {
      this.articles.unshift({
        ...article,
        id: Date.now(),
        createdAt: new Date().toLocaleString('zh-CN'),
        updatedAt: new Date().toLocaleString('zh-CN')
      })
      this.updateStats()
    },
    
    // 更新文章
    updateArticle(id, updates) {
      const index = this.articles.findIndex(a => a.id == id)
      if (index > -1) {
        this.articles[index] = {
          ...this.articles[index],
          ...updates,
          updatedAt: new Date().toLocaleString('zh-CN')
        }

        // 如果当前文章正在被编辑，也更新currentArticle
        if (this.currentArticle && this.currentArticle.id == id) {
          this.currentArticle = {
            ...this.currentArticle,
            ...updates,
            updatedAt: new Date().toLocaleString('zh-CN')
          }
        }

        this.updateStats()
        return true
      }
      return false
    },
    
    // 删除文章
    async deleteArticle(id) {
      try {
        const response = await deleteArticle(id)
        if (response.success) {
          // 从本地数组中删除
          const index = this.articles.findIndex(a => a.id === id)
          if (index > -1) {
            this.articles.splice(index, 1)
          }
          // 更新统计数据
          await this.updateStats()
          return true
        } else {
          throw new Error(response.error || '删除失败')
        }
      } catch (error) {
        console.error('[Store] 删除文章失败:', error)
        throw error
      }
    },
    
    // 批量操作
    batchUpdateStatus(ids, status) {
      ids.forEach(id => {
        const index = this.articles.findIndex(a => a.id === id)
        if (index > -1) {
          this.articles[index].status = status
          this.articles[index].updatedAt = new Date().toLocaleString('zh-CN')
        }
      })
      this.updateStats()
    },
    
    // 清空错误
    clearError() {
      this.error = null
    }
  }
})
