// 全局样式 - 参考 spaceship.com 的现代化设计
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  // 主色调 - 黑白反差主题
  --primary-color: #000000;
  --primary-light: #333333;
  --primary-dark: #000000;

  // 背景色
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #f1f3f4;
  --bg-card: #ffffff;

  // 文字颜色
  --text-primary: #000000;
  --text-secondary: #666666;
  --text-muted: #999999;

  // 边框和分割线
  --border-color: #e5e7eb;
  --border-light: #d1d5db;
  
  // 状态颜色
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;
  
  // 阴影
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  
  // 圆角
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  font-size: 14px;
  overflow-x: hidden;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;

  &:hover {
    background: var(--text-muted);
  }
}

// 通用类
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

.card {
  background: var(--bg-card);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  
  &:hover {
    border-color: var(--border-light);
    box-shadow: var(--shadow-lg);
  }
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-md);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &.btn-primary {
    background: var(--primary-color);
    color: white;
    
    &:hover:not(:disabled) {
      background: var(--primary-dark);
      transform: translateY(-1px);
    }
  }
  
  &.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    
    &:hover:not(:disabled) {
      background: var(--bg-secondary);
      border-color: var(--border-light);
    }
  }
  
  &.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    
    &:hover:not(:disabled) {
      background: var(--bg-tertiary);
      color: var(--text-primary);
    }
  }
}

// 输入框样式
.input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  color: var(--text-primary);
  font-size: 14px;
  transition: all 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }
  
  &::placeholder {
    color: var(--text-muted);
  }
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

// 响应式
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-md);
  }
  
  .card {
    padding: var(--spacing-md);
  }
}

// Element Plus 主题覆盖
.el-menu {
  background-color: var(--bg-secondary) !important;
  border-right: 1px solid var(--border-color) !important;
}

.el-menu-item {
  color: var(--text-secondary) !important;
  
  &:hover {
    background-color: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
  }
  
  &.is-active {
    background-color: var(--primary-color) !important;
    color: white !important;
  }
}

.el-card {
  background-color: var(--bg-card) !important;
  border: 1px solid var(--border-color) !important;
  
  .el-card__body {
    color: var(--text-primary) !important;
  }
}

.el-input__wrapper {
  background-color: var(--bg-tertiary) !important;
  border: 1px solid var(--border-color) !important;
  
  &:hover {
    border-color: var(--border-light) !important;
  }
  
  &.is-focus {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 1px var(--primary-color) !important;
  }
}

.el-input__inner {
  color: var(--text-primary) !important;
  
  &::placeholder {
    color: var(--text-muted) !important;
  }
}

.el-button--primary {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  
  &:hover {
    background-color: var(--primary-dark) !important;
    border-color: var(--primary-dark) !important;
  }
}
