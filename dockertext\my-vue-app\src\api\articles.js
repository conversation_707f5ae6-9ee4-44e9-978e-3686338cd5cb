// 文章API - 数据库操作
import axios from 'axios'

// 获取API基础URL，支持环境变量配置
const getApiBaseUrl = () => {
  // 开发环境
  if (import.meta.env.DEV) {
    return import.meta.env.VITE_API_BASE_URL || 'http://localhost:9485/api'
  }

  // 生产环境 - 使用相对路径或环境变量
  if (import.meta.env.VITE_API_BASE_URL) {
    return import.meta.env.VITE_API_BASE_URL
  }

  // 默认使用相对路径（适用于同域部署）
  return '/api'
}

// 创建axios实例
const api = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 10000,
  headers: {
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  }
})

// 解析文章文件内容
export const parseArticleContent = (content) => {
  const lines = content.split('\n')
  
  // 提取话题
  let topic = ''
  let newsCount = 0
  let sources = []
  
  for (let i = 0; i < Math.min(10, lines.length); i++) {
    const line = lines[i].trim()
    if (line.startsWith('话题:')) {
      topic = line.replace('话题:', '').trim()
    } else if (line.startsWith('相关新闻数:')) {
      const match = line.match(/(\d+)/)
      if (match) newsCount = parseInt(match[1])
    } else if (line.startsWith('素材来源:')) {
      sources = line.replace('素材来源:', '').split(',').map(s => s.trim())
    }
  }
  
  // 找到分割线位置
  let contentStartIndex = -1
  let summaryStartIndex = -1
  let articleStartIndex = -1
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()
    if (line.includes('============================================================')) {
      if (contentStartIndex === -1) {
        contentStartIndex = i + 1
      } else if (articleStartIndex === -1) {
        articleStartIndex = i + 1
      }
    } else if (line.includes('【新闻要点总结】')) {
      summaryStartIndex = i
    } else if (line.includes('【爆款文章】')) {
      articleStartIndex = i
    }
  }
  
  // 提取摘要部分
  let summary = ''
  if (summaryStartIndex > -1 && articleStartIndex > -1) {
    const summaryLines = lines.slice(summaryStartIndex + 1, articleStartIndex - 1)
    summary = summaryLines.join('\n').trim()
  }
  
  // 提取文章内容
  let articleContent = ''
  let title = topic
  
  if (articleStartIndex > -1) {
    const articleLines = lines.slice(articleStartIndex + 1)
    articleContent = articleLines.join('\n').trim()
    
    // 提取文章标题
    const titleMatch = articleContent.match(/\*\*《(.+?)》\*\*/)
    if (titleMatch) {
      title = titleMatch[1]
    }
  }
  
  // 生成简短摘要
  const shortSummary = summary.replace(/[#*`]/g, '').substring(0, 200) + '...'
  
  return {
    title,
    topic,
    newsCount,
    sources,
    summary: shortSummary,
    fullSummary: summary,
    content: articleContent,
    wordCount: articleContent.replace(/[#*`\-\s]/g, '').length
  }
}

// 生成分类
export const generateCategory = (topic, content) => {
  const text = (topic + ' ' + content).toLowerCase()
  
  if (text.includes('nba') || text.includes('篮球') || text.includes('足球') || text.includes('体育') || text.includes('赛事')) {
    return 'sports'
  } else if (text.includes('科技') || text.includes('ai') || text.includes('技术') || text.includes('互联网')) {
    return 'technology'
  } else if (text.includes('经济') || text.includes('金融') || text.includes('股市') || text.includes('财经')) {
    return 'finance'
  } else if (text.includes('国际') || text.includes('外交') || text.includes('政治') || text.includes('中东') || text.includes('美国')) {
    return 'international'
  } else if (text.includes('明星') || text.includes('娱乐') || text.includes('电影') || text.includes('名人')) {
    return 'entertainment'
  } else if (text.includes('军事') || text.includes('国防') || text.includes('武器')) {
    return 'military'
  } else {
    return 'social'
  }
}

// 生成标签
export const generateTags = (topic, content) => {
  const tags = []
  const text = (topic + ' ' + content).toLowerCase()
  
  // 标签关键词映射
  const tagKeywords = {
    'NBA': ['nba', '篮球', '球员'],
    '足球': ['足球', '世界杯', '欧洲杯'],
    '科技': ['科技', 'ai', '人工智能', '互联网'],
    '金融': ['金融', '经济', '股市', '投资'],
    '国际': ['国际', '外交', '全球'],
    '中东': ['中东', '以色列', '巴勒斯坦'],
    '美国': ['美国', '拜登', '特朗普'],
    '中国': ['中国', '北京', '上海'],
    '娱乐': ['娱乐', '明星', '电影', '电视'],
    '军事': ['军事', '国防', '武器', '战争'],
    '健康': ['健康', '医疗', '疫情'],
    '教育': ['教育', '学校', '大学'],
    '房地产': ['房地产', '楼市', '房价'],
    '汽车': ['汽车', '新能源', '特斯拉']
  }
  
  for (const [tag, keywords] of Object.entries(tagKeywords)) {
    if (keywords.some(keyword => text.includes(keyword))) {
      tags.push(tag)
    }
  }
  
  return tags.slice(0, 6) // 最多6个标签
}

// 读取数据库文章列表
export const loadLocalArticles = async (params = {}) => {
  try {
    const response = await api.get('/articles', { params })
    return response.data
  } catch (error) {
    console.error('加载文章失败:', error)
    return {
      success: false,
      error: error.message || '网络请求失败'
    }
  }
}

// 读取单个文章详情
export const loadArticleFile = async (articleId) => {
  try {
    const response = await api.get(`/articles/${articleId}`)
    return response.data
  } catch (error) {
    console.error('加载文章详情失败:', error)
    return {
      success: false,
      error: error.message || '网络请求失败'
    }
  }
}

// 获取文章统计数据
export const getArticleStats = async () => {
  try {
    const response = await api.get('/stats')
    return response.data
  } catch (error) {
    console.error('获取统计数据失败:', error)
    return {
      success: false,
      error: error.message || '网络请求失败'
    }
  }
}

// 保存文章到数据库
export const saveArticleToDatabase = async (id, articleData) => {
  try {
    const response = await api.put(`/articles/${id}`, articleData)
    return response.data
  } catch (error) {
    console.error('保存文章到数据库失败:', error)
    return {
      success: false,
      error: error.message || '保存失败'
    }
  }
}

// 创建新文章
export const createArticle = async (articleData) => {
  try {
    const response = await api.post('/articles', articleData)
    return response.data
  } catch (error) {
    console.error('创建文章失败:', error)
    return {
      success: false,
      error: error.message || '创建失败'
    }
  }
}

// 删除文章
export const deleteArticle = async (id) => {
  try {
    const response = await api.delete(`/articles/${id}`)
    return response.data
  } catch (error) {
    console.error('删除文章失败:', error)
    return {
      success: false,
      error: error.message || '删除失败'
    }
  }
}

// 搜索文章
export const searchArticles = async (query, filters = {}) => {
  try {
    // 这里应该调用后端API进行搜索
    const articles = await loadLocalArticles()

    if (!articles.success) return articles

    let results = articles.data

    // 搜索过滤
    if (query) {
      const searchQuery = query.toLowerCase()
      results = results.filter(article =>
        article.title.toLowerCase().includes(searchQuery) ||
        article.topic.toLowerCase().includes(searchQuery) ||
        article.summary.toLowerCase().includes(searchQuery)
      )
    }

    // 状态过滤
    if (filters.status) {
      results = results.filter(article => article.status === filters.status)
    }

    // 分类过滤
    if (filters.category) {
      results = results.filter(article => article.category === filters.category)
    }

    return {
      success: true,
      data: results,
      total: results.length
    }
  } catch (error) {
    return {
      success: false,
      error: error.message
    }
  }
}

export default {
  loadLocalArticles,
  loadArticleFile,
  getArticleStats,
  searchArticles,
  parseArticleContent,
  generateCategory,
  generateTags
}
