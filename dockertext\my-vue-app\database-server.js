// 基于MySQL数据库的文章管理服务器
import 'dotenv/config'
import express from 'express'
import mysql from 'mysql2/promise'
import cors from 'cors'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const app = express()
const PORT = process.env.PORT || 9486
const HOST = process.env.HOST || '0.0.0.0'

// 配置CORS
const corsOptions = {
  origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
  credentials: true
}

// 中间件
app.use(cors(corsOptions))
app.use(express.json({ limit: '50mb' }))

// 服务静态文件（前端构建后的文件）
app.use(express.static(path.join(__dirname, 'dist')))

// 数据库配置 - 从环境变量读取
const DB_CONFIG = {
  host: process.env.DB_HOST || 'mysql-24e746d3-kbxy2365806687-c9eb.b.aivencloud.com',
  port: parseInt(process.env.DB_PORT) || 13304,
  user: process.env.DB_USER || 'avnadmin',
  password: process.env.DB_PASSWORD || 'AVNS_kX8_YlNEfE3RquyPaG6',
  database: process.env.DB_NAME || 'defaultdb',
  charset: 'utf8mb4',
  ssl: {
    rejectUnauthorized: false
  },
  timezone: '+00:00'
}

// 创建数据库连接池
const pool = mysql.createPool({
  ...DB_CONFIG,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
})

// 测试数据库连接
async function testConnection() {
  try {
    const connection = await pool.getConnection()
    console.log('✅ 数据库连接成功')
    connection.release()
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message)
  }
}

// 禁用缓存的中间件
const noCache = (req, res, next) => {
  res.set({
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  })
  next()
}

// ==================== API 路由 ====================

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'smart-notes-api',
    version: '1.0.0'
  })
})

// 获取文章列表（分页）
app.get('/api/articles', noCache, async (req, res) => {
  try {
    const { page = 1, limit = 20, topic, status, date, sortBy = 'created_at', sortOrder = 'desc' } = req.query
    const offset = (parseInt(page) - 1) * parseInt(limit)

    // 如果没有指定日期，默认使用今天（中国时间）
    const targetDate = date || new Date(new Date().getTime() + 8 * 60 * 60 * 1000).toISOString().split('T')[0]

    console.log(`[文章列表] 页码: ${page}, 每页: ${limit}, 日期: ${targetDate}, 话题: ${topic || '全部'}, 状态: ${status || '全部'}, 排序: ${sortBy} ${sortOrder}`)

    // 构建查询条件
    let whereConditions = []
    let articlesParams = []
    let countParams = []

    // 日期筛选（必须，转换为中国时间）
    whereConditions.push('DATE(DATE_ADD(created_at, INTERVAL 8 HOUR)) = ?')
    articlesParams.push(targetDate)
    countParams.push(targetDate)

    // 话题搜索
    if (topic) {
      whereConditions.push('(topic LIKE ? OR title LIKE ? OR content LIKE ?)')
      articlesParams.push(`%${topic}%`, `%${topic}%`, `%${topic}%`)
      countParams.push(`%${topic}%`, `%${topic}%`, `%${topic}%`)
    }

    // 状态筛选
    if (status) {
      whereConditions.push('status = ?')
      articlesParams.push(status)
      countParams.push(status)
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : ''

    // 构建完整的WHERE条件（转换为中国时间）
    let fullWhereClause = `WHERE DATE(DATE_ADD(created_at, INTERVAL 8 HOUR)) = '${targetDate}'`

    // 添加状态筛选
    if (status && status !== '全部') {
      fullWhereClause += ` AND status = '${status}'`
    }

    // 添加话题筛选
    if (topic && topic !== '全部') {
      fullWhereClause += ` AND (topic LIKE '%${topic}%' OR title LIKE '%${topic}%')`
    }

    // 构建排序字段映射
    const sortFieldMap = {
      'createdAt': 'created_at',
      'updatedAt': 'updated_at',
      'title': 'title',
      'views': 'views'
    }

    const dbSortField = sortFieldMap[sortBy] || 'created_at'
    const dbSortOrder = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC'

    // 查询文章列表
    const articlesQuery = `
      SELECT id, topic, title, word_count, news_count, status, created_at, updated_at
      FROM articles
      ${fullWhereClause}
      ORDER BY ${dbSortField} ${dbSortOrder}
      LIMIT ${parseInt(limit)} OFFSET ${offset}
    `

    console.log('[SQL] 文章查询:', articlesQuery)

    const [articles] = await pool.execute(articlesQuery)

    // 查询总数
    const countQuery = `SELECT COUNT(*) as total FROM articles ${fullWhereClause}`
    console.log('[SQL] 计数查询:', countQuery)

    const [countResult] = await pool.execute(countQuery)
    const total = countResult[0].total

    console.log(`[文章列表] 查询到 ${articles.length} 篇文章，总计 ${total} 篇`)

    res.json({
      success: true,
      data: articles.map(article => ({
        ...article,
        fileName: `article_${article.id}.txt`,
        summary: article.topic ? article.topic.substring(0, 200) + '...' : '暂无摘要',
        category: 'database',
        tags: []
      })),
      total,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      },
      currentDate: targetDate
    })

  } catch (error) {
    console.error('[错误] 获取文章列表失败:', error)
    res.json({
      success: false,
      error: error.message
    })
  }
})

// 获取单个文章详情
app.get('/api/articles/:id', noCache, async (req, res) => {
  try {
    const { id } = req.params
    console.log(`[文章详情] 获取文章 ID: ${id}`)

    const query = 'SELECT * FROM articles WHERE id = ?'
    const [articles] = await pool.execute(query, [id])

    if (articles.length === 0) {
      return res.json({
        success: false,
        error: '文章不存在'
      })
    }

    const article = articles[0]

    // 解析JSON字段
    let news_sources = []
    let top_news_titles = []

    try {
      if (article.news_sources) {
        news_sources = JSON.parse(article.news_sources)
      }
    } catch (e) {
      console.warn('解析 news_sources 失败:', e.message)
    }

    try {
      if (article.top_news_titles) {
        top_news_titles = JSON.parse(article.top_news_titles)
      }
    } catch (e) {
      console.warn('解析 top_news_titles 失败:', e.message)
    }

    res.json({
      success: true,
      data: {
        ...article,
        news_sources,
        top_news_titles,
        // 兼容前端字段
        summary: article.news_points || article.topic.substring(0, 200) + '...',
        category: 'database',
        tags: []
      }
    })

  } catch (error) {
    console.error('[错误] 获取文章详情失败:', error)
    res.json({
      success: false,
      error: error.message
    })
  }
})

// 获取统计数据
app.get('/api/stats', noCache, async (req, res) => {
  try {
    console.log('[统计数据] 开始查询')

    // 总文章数
    const [totalResult] = await pool.execute('SELECT COUNT(*) as total FROM articles')
    const totalArticles = totalResult[0].total

    // 各状态文章数
    const [statusResult] = await pool.execute(`
      SELECT status, COUNT(*) as count
      FROM articles
      GROUP BY status
    `)

    let draftArticles = 0
    let publishedArticles = 0
    let archivedArticles = 0

    statusResult.forEach(row => {
      switch(row.status) {
        case 'draft':
          draftArticles = row.count
          break
        case 'published':
          publishedArticles = row.count
          break
        case 'archived':
          archivedArticles = row.count
          break
      }
    })

    // 今日文章数（中国时间）
    const today = new Date(new Date().getTime() + 8 * 60 * 60 * 1000).toISOString().split('T')[0]
    const [todayResult] = await pool.execute(
      'SELECT COUNT(*) as count FROM articles WHERE DATE(DATE_ADD(created_at, INTERVAL 8 HOUR)) = ?',
      [today]
    )
    const todayArticles = todayResult[0].count

    console.log(`[统计数据] 总计: ${totalArticles}, 草稿: ${draftArticles}, 已发布: ${publishedArticles}, 今日: ${todayArticles}`)

    res.json({
      success: true,
      data: {
        totalArticles,
        draftArticles,
        publishedArticles,
        archivedArticles,
        todayArticles
      }
    })

  } catch (error) {
    console.error('[错误] 获取统计数据失败:', error)
    res.json({
      success: false,
      error: error.message
    })
  }
})

// 创建新文章
app.post('/api/articles', async (req, res) => {
  try {
    const { topic, title, content, news_points, news_count, news_sources, top_news_titles } = req.body

    console.log(`[创建文章] 标题: ${title || '无标题'}`)

    // 计算字数
    const word_count = content ? content.length : 0

    // 处理JSON字段
    const sources_json = JSON.stringify(news_sources || [])
    const titles_json = JSON.stringify(top_news_titles || [])

    const query = `
      INSERT INTO articles (
        topic, title, content, news_points, word_count, 
        news_count, news_sources, top_news_titles
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `

    const values = [
      topic || '',
      title || '',
      content || '',
      news_points || '',
      word_count,
      news_count || 0,
      sources_json,
      titles_json
    ]

    const [result] = await pool.execute(query, values)

    console.log(`[创建文章] 成功，新文章 ID: ${result.insertId}`)

    res.json({
      success: true,
      data: {
        id: result.insertId,
        message: '文章创建成功'
      }
    })

  } catch (error) {
    console.error('[错误] 创建文章失败:', error)
    res.json({
      success: false,
      error: error.message
    })
  }
})

// 更新文章
app.put('/api/articles/:id', async (req, res) => {
  try {
    const { id } = req.params
    const { title, content, status, news_points } = req.body

    console.log(`[更新文章] ID: ${id}, 标题: ${title}`)

    // 构建更新字段
    const updateFields = []
    const params = []

    if (title !== undefined) {
      updateFields.push('title = ?')
      params.push(title)
    }

    if (content !== undefined) {
      updateFields.push('content = ?')
      updateFields.push('word_count = ?')
      params.push(content)
      params.push(content.length)
    }

    if (status !== undefined) {
      updateFields.push('status = ?')
      params.push(status)
    }

    if (news_points !== undefined) {
      updateFields.push('news_points = ?')
      params.push(news_points)
    }

    if (updateFields.length === 0) {
      return res.json({
        success: false,
        error: '没有要更新的字段'
      })
    }

    // 添加更新时间
    updateFields.push('updated_at = CURRENT_TIMESTAMP')
    params.push(id)

    const query = `UPDATE articles SET ${updateFields.join(', ')} WHERE id = ?`
    const [result] = await pool.execute(query, params)

    if (result.affectedRows === 0) {
      return res.json({
        success: false,
        error: '文章不存在或未更新'
      })
    }

    console.log(`[更新文章] 成功更新文章 ID: ${id}`)

    res.json({
      success: true,
      message: '文章更新成功'
    })

  } catch (error) {
    console.error('[错误] 更新文章失败:', error)
    res.json({
      success: false,
      error: error.message
    })
  }
})

// 删除文章
app.delete('/api/articles/:id', async (req, res) => {
  try {
    const { id } = req.params
    console.log(`[删除文章] ID: ${id}`)

    const query = 'DELETE FROM articles WHERE id = ?'
    const [result] = await pool.execute(query, [id])

    if (result.affectedRows === 0) {
      return res.json({
        success: false,
        error: '文章不存在'
      })
    }

    console.log(`[删除文章] 成功删除文章 ID: ${id}`)

    res.json({
      success: true,
      message: '文章删除成功'
    })

  } catch (error) {
    console.error('[错误] 删除文章失败:', error)
    res.json({
      success: false,
      error: error.message
    })
  }
})

// SPA路由处理中间件
app.use((req, res, next) => {
  // 如果是API路由、健康检查或静态资源，继续处理
  if (req.path.startsWith('/api/') ||
      req.path === '/health' ||
      req.path.includes('.') ||
      req.method !== 'GET') {
    return next()
  }

  // 对于所有其他GET请求，返回index.html
  res.sendFile(path.join(__dirname, 'dist', 'index.html'))
})

// 启动服务器
app.listen(PORT, HOST, async () => {
  console.log(`🚀 数据库服务器启动成功`)
  console.log(`📍 地址: http://${HOST === '0.0.0.0' ? 'localhost' : HOST}:${PORT}`)
  console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`)
  await testConnection()
})
